<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\ProductSku;
use App\Models\DataSource;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;
use App\Imports\ProductImport;

class ProductController extends Controller
{
    /**
     * 显示商品列表页面
     */
    public function index()
    {
        return view('products.index');
    }

    /**
     * 显示添加商品页面
     */
    public function create()
    {
        $dataSources = DataSource::where('status', 'active')->get();
        return view('products.create', compact('dataSources'));
    }

    /**
     * 显示批量导入页面
     */
    public function importCreate()
    {
        return view('products.import');
    }

    /**
     * 处理单个商品添加
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'platform' => 'required|string|in:淘宝,天猫,京东,拼多多,其他',
            'url' => 'required|url',
            'price' => 'nullable|numeric|min:0',
            'data_source_id' => 'required|exists:data_sources,id',
            'description' => 'nullable|string',
            'auto_monitor' => 'boolean'
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            DB::beginTransaction();

            // 创建商品
            $product = Product::create([
                'name' => $request->name,
                'platform' => $request->platform,
                'url' => $request->url,
                'description' => $request->description,
                'data_source_id' => $request->data_source_id,
                'status' => $request->auto_monitor ? 'active' : 'inactive'
            ]);

            // 创建商品SKU
            if ($request->price) {
                ProductSku::create([
                    'product_id' => $product->id,
                    'sku_code' => 'DEFAULT',
                    'price' => $request->price,
                    'stock' => 0
                ]);
            }

            DB::commit();

            return redirect()->route('products.index')->with('success', '商品添加成功！');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', '添加失败：' . $e->getMessage())->withInput();
        }
    }

    /**
     * 处理批量导入
     */
    public function import(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'excel_file' => 'required|file|mimes:xlsx,xls,csv|max:10240',
            'data_source_id' => 'required|exists:data_sources,id'
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator);
        }

        try {
            $import = new ProductImport($request->data_source_id);
            Excel::import($import, $request->file('excel_file'));

            return redirect()->route('products.index')->with('success', 
                '导入完成！成功导入 ' . $import->getSuccessCount() . ' 个商品。');
        } catch (\Exception $e) {
            return back()->with('error', '导入失败：' . $e->getMessage());
        }
    }

    /**
     * 下载导入模板
     */
    public function downloadTemplate()
    {
        $templatePath = public_path('templates/product-import-template.xlsx');
        
        if (!file_exists($templatePath)) {
            return back()->with('error', '模板文件不存在');
        }

        return response()->download($templatePath, '商品导入模板.xlsx');
    }

    /**
     * 批量输入商品
     */
    public function bulkCreate(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'data_source_id' => 'required|exists:data_sources,id',
            'products' => 'required|string',
            'platform' => 'required|string|in:淘宝,天猫,京东,拼多多,其他',
            'auto_monitor' => 'boolean'
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'errors' => $validator->errors()]);
        }

        try {
            DB::beginTransaction();

            $lines = array_filter(array_map('trim', explode("\n", $request->products)));
            $successCount = 0;
            $errors = [];

            foreach ($lines as $index => $line) {
                if (empty($line)) continue;

                // 检查是否是URL
                if (filter_var($line, FILTER_VALIDATE_URL)) {
                    // 从URL中提取商品名称
                    $name = $this->extractProductNameFromUrl($line);
                    
                    $product = Product::create([
                        'name' => $name,
                        'platform' => $request->platform,
                        'url' => $line,
                        'data_source_id' => $request->data_source_id,
                        'status' => $request->auto_monitor ? 'active' : 'inactive'
                    ]);

                    $successCount++;
                } else {
                    $errors[] = "第 " . ($index + 1) . " 行：无效的URL格式";
                }
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => "成功添加 {$successCount} 个商品" . 
                           (count($errors) > 0 ? "，" . count($errors) . " 个失败" : ""),
                'errors' => $errors
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['success' => false, 'message' => '批量添加失败：' . $e->getMessage()]);
        }
    }

    /**
     * 从URL中提取商品名称
     */
    private function extractProductNameFromUrl($url)
    {
        $domain = parse_url($url, PHP_URL_HOST);
        
        if (strpos($domain, 'taobao.com') !== false || strpos($domain, 'tmall.com') !== false) {
            return '淘宝商品 - ' . substr(md5($url), 0, 8);
        } elseif (strpos($domain, 'jd.com') !== false) {
            return '京东商品 - ' . substr(md5($url), 0, 8);
        } elseif (strpos($domain, 'pinduoduo.com') !== false) {
            return '拼多多商品 - ' . substr(md5($url), 0, 8);
        } else {
            return '商品 - ' . substr(md5($url), 0, 8);
        }
    }
} 
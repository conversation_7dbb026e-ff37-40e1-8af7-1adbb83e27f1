<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\DataSource;
use App\Models\TaskGroup;

class UpdateDataSourceModuleTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 获取第一个渠道价格监测任务分组
        $channelPriceGroup = TaskGroup::where('module_type', 'channel_price')->first();
        
        if ($channelPriceGroup) {
            // 更新所有没有module_type的数据源为渠道价格监测类型
            DataSource::whereNull('module_type')
                ->update([
                    'module_type' => 'channel_price',
                    'task_group_id' => $channelPriceGroup->id,
                    'sync_frequency' => 'manual',
                    'sync_status' => 'pending',
                ]);
            
            $this->command->info('已更新现有数据源的模块类型为渠道价格监测');
        }
        
        // 创建一些示例数据源
        $this->createSampleDataSources();
    }
    
    private function createSampleDataSources()
    {
        $taskGroups = TaskGroup::all();
        
        foreach ($taskGroups as $group) {
            // 为每个任务分组创建一个示例数据源
            $existingCount = DataSource::where('task_group_id', $group->id)->count();
            
            if ($existingCount == 0) {
                DataSource::create([
                    'name' => $group->name . ' - 示例数据源',
                    'description' => '这是一个示例数据源，用于演示 ' . $group->name,
                    'module_type' => $group->module_type,
                    'task_group_id' => $group->id,
                    'user_id' => $group->user_id,
                    'api_url' => 'https://api.example.com/' . $group->module_type,
                    'sync_frequency' => 'daily',
                    'sync_status' => 'pending',
                    'status' => 'completed', // 使用enum中存在的值
                    'platform' => 'taobao', // 添加必需字段
                    'source_type' => 'api', // 添加必需字段
                    'is_active' => true,
                    'official_guide_price' => $group->module_type === 'channel_price' ? 99.99 : null,
                    'my_guide_price' => $group->module_type === 'competitor_dynamics' ? 89.99 : null,
                    'search_keyword' => $group->module_type === 'similar_products' ? '示例商品' : null,
                ]);
            }
        }
        
        $this->command->info('已创建示例数据源');
    }
}

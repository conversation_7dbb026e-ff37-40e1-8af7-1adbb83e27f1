<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\V1\TaskGroupController as V1TaskGroupController;
use App\Http\Controllers\Api\V1\MonitorTaskController as V1MonitorTaskController;
use App\Http\Controllers\Api\V1\AlertRuleController as V1AlertRuleController;
use App\Http\Controllers\Api\V1\ProductController as V1ProductController;
use App\Http\Controllers\Api\CompetitorMetricsController;
use App\Http\Controllers\Api\ProductSearchController;
use App\Http\Controllers\AlertLogController;
use App\Http\Controllers\PerformanceMonitorController;
use App\Http\Controllers\OfficialGuidePriceController;
use App\Http\Controllers\DataSourceController;
use App\Http\Controllers\CompetitorController;
use App\Http\Controllers\GroupReportingController;
use App\Http\Controllers\TaskGroupController;
use App\Http\Controllers\DashboardController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// --- Public Routes ---

// 公共平台数据路由（无需认证）
Route::get('/platforms', function() {
    return response()->json([
        'success' => true,
        'data' => [
            ['id' => 1, 'name' => '淘宝', 'code' => 'taobao'],
            ['id' => 2, 'name' => '天猫', 'code' => 'tmall'],
            ['id' => 3, 'name' => '京东', 'code' => 'jd'],
            ['id' => 4, 'name' => '拼多多', 'code' => 'pdd'],
            ['id' => 5, 'name' => '小红书', 'code' => 'xiaohongshu'],
            ['id' => 6, 'name' => '抖音', 'code' => 'douyin']
        ]
    ]);
})->name('api.platforms.public');

// Dashboard API routes
Route::prefix('dashboard')->group(function () {
    Route::get('/stats', function () {
        return response()->json([
            'success' => true,
            'data' => [
                'taskCount' => 156,
                'productCount' => 1234,
                'priceAlertCount' => 23,
                'todayUpdateCount' => 89
            ]
        ]);
    });

    Route::get('/charts/{type}', function ($type) {
        $data = [];

        switch ($type) {
            case 'price-trend':
                $labels = [];
                $prices = [];
                for ($i = 29; $i >= 0; $i--) {
                    $date = now()->subDays($i);
                    $labels[] = $date->format('m-d');
                    $prices[] = rand(50, 150);
                }
                $data = ['labels' => $labels, 'prices' => $prices];
                break;

            case 'category-distribution':
                $data = [
                    'labels' => ['电子产品', '服装鞋帽', '家居用品', '食品饮料', '美妆护肤', '其他'],
                    'values' => [35, 25, 15, 12, 8, 5]
                ];
                break;

            case 'monitoring-trend':
                $labels = [];
                $tasks = [];
                $alerts = [];
                for ($i = 6; $i >= 0; $i--) {
                    $date = now()->subDays($i);
                    $labels[] = $date->format('D');
                    $tasks[] = rand(20, 70);
                    $alerts[] = rand(1, 15);
                }
                $data = ['labels' => $labels, 'tasks' => $tasks, 'alerts' => $alerts];
                break;
        }

        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    });

    Route::get('/chart-data', [DashboardController::class, 'getChartData']);
});

// Performance monitoring routes
Route::prefix('performance')->group(function () {
    Route::get('/stats', [PerformanceMonitorController::class, 'getTaskPerformanceStats']);
    Route::get('/execution-times', [PerformanceMonitorController::class, 'getTaskExecutionTimes']);
    Route::get('/queue-analysis', [PerformanceMonitorController::class, 'getQueueDepthAnalysis']);
    Route::get('/retry-analysis', [PerformanceMonitorController::class, 'getTaskRetryAnalysis']);
    Route::get('/platform-comparison', [PerformanceMonitorController::class, 'getPlatformPerformanceComparison']);
    Route::get('/bottleneck-analysis', [PerformanceMonitorController::class, 'getTaskBottleneckAnalysis']);
    Route::get('/snapshot', [PerformanceMonitorController::class, 'getTaskPerformanceSnapshot']);
    Route::get('/dashboard', [PerformanceMonitorController::class, 'getDashboardData']);
    Route::get('/trends', [PerformanceMonitorController::class, 'getPerformanceTrends']);
    Route::post('/record/task-execution', [PerformanceMonitorController::class, 'recordTaskExecution']);
    Route::post('/record/task-retry', [PerformanceMonitorController::class, 'recordTaskRetry']);
    Route::post('/record/queue-depth', [PerformanceMonitorController::class, 'recordQueueDepth']);
    Route::post('/record/hourly-metrics', [PerformanceMonitorController::class, 'recordHourlyMetrics']);
});


// --- Authenticated Routes ---

Route::middleware(['auth:sanctum,web'])->group(function () {
    Route::get('/user', function (Request $request) {
        return $request->user();
    });

    // Resource Routes
    Route::apiResource('categories', \App\Http\Controllers\CategoryController::class);
    Route::apiResource('products', V1ProductController::class);
    Route::apiResource('monitor-tasks', V1MonitorTaskController::class);
    Route::apiResource('task-groups', V1TaskGroupController::class);
    Route::apiResource('alert-rules', V1AlertRuleController::class);
    Route::apiResource('competitor-metrics', CompetitorMetricsController::class);

    // Product Routes
    Route::get('/products/search', [ProductSearchController::class, 'index'])->name('products.search');
    Route::get('/products/{product}/skus', function(\App\Models\Product $product) {
        return response()->json([
            'success' => true,
            'data' => $product->skus()->select('id', 'sku_code', 'title', 'price')->get(),
            'message' => '产品SKU获取成功'
        ]);
    })->name('products.skus');

    // Official Guide Price Routes
    Route::prefix('official-guide-prices')->name('official-guide-prices.')->group(function () {
        Route::get('stats', [OfficialGuidePriceController::class, 'stats'])->name('stats');
        Route::post('set-single', [OfficialGuidePriceController::class, 'setSingle'])->name('set-single');
        Route::post('set-batch', [OfficialGuidePriceController::class, 'setBatch'])->name('set-batch');
        Route::post('import-csv', [OfficialGuidePriceController::class, 'importCsv'])->name('import-csv');
        Route::post('calculate-auto', [OfficialGuidePriceController::class, 'calculateAuto'])->name('calculate-auto');
        Route::post('batch-calculate-auto', [OfficialGuidePriceController::class, 'batchCalculateAuto'])->name('batch-calculate-auto');
        Route::get('skus-needing', [OfficialGuidePriceController::class, 'getSkusNeeding'])->name('skus-needing');
        Route::get('download-template', [OfficialGuidePriceController::class, 'downloadTemplate'])->name('download-template');
        Route::delete('clear', [OfficialGuidePriceController::class, 'clear'])->name('clear');
    });

    // Alert Rule Routes
    Route::post('/alert-rules/batch-delete', [\App\Http\Controllers\AlertRuleController::class, 'batchDestroy'])->name('alert-rules.batch-destroy');
    Route::patch('/alert-rules/{alertRule}/toggle-status', [\App\Http\Controllers\AlertRuleController::class, 'toggleStatus'])->name('alert-rules.toggle-status');
    Route::get('/alert-rules/stats/overview', [\App\Http\Controllers\AlertRuleController::class, 'stats'])->name('alert-rules.stats');
    Route::get('/alert-rules/options/all', [\App\Http\Controllers\AlertRuleController::class, 'options'])->name('alert-rules.options');
    Route::post('/alert-rules/validate', [\App\Http\Controllers\AlertRuleController::class, 'validateRule']);
    Route::get('/alert-rules/config/templates', [\App\Http\Controllers\AlertRuleController::class, 'getConfigTemplates']);
    Route::post('/alert-rules/test', [\App\Http\Controllers\AlertRuleController::class, 'test']);

    // Alert Log Routes
    Route::get('/alert-logs', [AlertLogController::class, 'index'])->name('alert-logs.index');
    Route::get('/alert-logs/stats', [AlertLogController::class, 'stats'])->name('alert-logs.stats');
    Route::get('/alert-logs/{id}', [AlertLogController::class, 'show'])->name('alert-logs.show');
    Route::patch('/alert-logs/{id}/mark-read', [AlertLogController::class, 'markAsRead'])->name('alert-logs.mark-read');
    Route::patch('/alert-logs/{id}/mark-resolved', [AlertLogController::class, 'markAsResolved'])->name('alert-logs.mark-resolved');
    Route::post('/alert-logs/batch-update', [AlertLogController::class, 'batchUpdate'])->name('alert-logs.batch-update');
    Route::delete('/alert-logs/{id}', [AlertLogController::class, 'destroy'])->name('alert-logs.destroy');
    Route::post('/alert-logs/batch-delete', [AlertLogController::class, 'batchDestroy'])->name('alert-logs.batch-destroy');

    // Task Group Routes
    Route::get('/task-groups/search', [TaskGroupController::class, 'search']);
    Route::get('/task-groups/unassigned-tasks', [TaskGroupController::class, 'unassignedTasks'])->name('task-groups.unassigned-tasks');
    Route::get('/task-groups/distribution-analysis', [TaskGroupController::class, 'distributionAnalysis'])->name('task-groups.distribution-analysis');
    Route::get('/task-groups/comparison-report', [TaskGroupController::class, 'getComparisonReport'])->name('task-groups.comparison-report');
    Route::post('/task-groups/auto-group', [TaskGroupController::class, 'autoGroup'])->name('task-groups.auto-group');
    Route::post('/task-groups/merge', [TaskGroupController::class, 'merge'])->name('task-groups.merge');
    Route::get('/task-groups/{taskGroup}', [TaskGroupController::class, 'show'])->name('task-groups.show');
    Route::put('/task-groups/{taskGroup}', [TaskGroupController::class, 'update'])->name('task-groups.update');
    Route::delete('/task-groups/{taskGroup}', [TaskGroupController::class, 'destroy'])->name('task-groups.destroy');
    Route::post('/task-groups/{taskGroup}/assign-tasks', [TaskGroupController::class, 'assignTasks'])->name('task-groups.assign-tasks');
    Route::post('/task-groups/{taskGroup}/remove-tasks', [TaskGroupController::class, 'removeTasks'])->name('task-groups.remove-tasks');
    Route::post('/task-groups/{taskGroup}/duplicate', [TaskGroupController::class, 'duplicate'])->name('task-groups.duplicate');
    Route::get('/task-groups/{taskGroup}/statistics', [TaskGroupController::class, 'statistics'])->name('task-groups.statistics');
    Route::get('/task-groups/{taskGroup}/report', [TaskGroupController::class, 'getReport'])->name('task-groups.report');
    Route::get('/task-groups/{taskGroup}/report-summary', [TaskGroupController::class, 'getReportSummary'])->name('task-groups.report-summary');
    Route::get('/task-groups/{taskGroup}/export-report', [TaskGroupController::class, 'exportReport'])->name('task-groups.export-report');
    Route::get('/task-groups/{id}/aggregate-report', [GroupReportingController::class, 'show'])->name('task-groups.aggregate-report');

    // Competitor Search Routes
    Route::prefix('competitors')->name('competitors.')->middleware('competitor.monitoring')->group(function () {
        Route::post('/search', [CompetitorController::class, 'search'])->name('search');
        Route::post('/search-similar', [CompetitorController::class, 'searchSimilar'])->name('search-similar');
        Route::post('/find-similar/{productId}', [CompetitorController::class, 'findSimilar'])->name('find-similar');
        Route::get('/suggestions', [CompetitorController::class, 'suggestions'])->name('suggestions');
        Route::post('/export', [CompetitorController::class, 'exportResults'])->name('export');
        Route::post('/clear-cache', [CompetitorController::class, 'clearCache'])->name('clear-cache');
        Route::get('/stats', [CompetitorController::class, 'getStats'])->name('stats');
    });

    // Data Sources API Routes
    Route::prefix('data-sources')->name('data-sources.')->group(function () {
        Route::get('/import-history', [DataSourceController::class, 'getImportHistory'])->name('import-history');
        Route::post('/import-excel', [DataSourceController::class, 'importExcel'])->name('import-excel');
        Route::get('/import-status/{id}', [DataSourceController::class, 'getImportStatus'])->name('import-status');
        Route::delete('/import-history/{id}', [DataSourceController::class, 'deleteImportHistory'])->name('delete-import-history');
    });

    // Platform API Routes
    Route::get('/platforms', function() {
        return response()->json([
            'success' => true,
            'data' => [
                ['id' => 1, 'name' => '淘宝', 'code' => 'taobao'],
                ['id' => 2, 'name' => '天猫', 'code' => 'tmall'],
                ['id' => 3, 'name' => '京东', 'code' => 'jd'],
                ['id' => 4, 'name' => '拼多多', 'code' => 'pdd'],
                ['id' => 5, 'name' => '小红书', 'code' => 'xiaohongshu'],
                ['id' => 6, 'name' => '抖音', 'code' => 'douyin']
            ]
        ]);
    })->name('platforms.index');

    // 数据源管理
    Route::apiResource('data-sources', DataSourceController::class)->except(['show', 'update']);
});

Route::prefix('v1')->group(function () {
    Route::apiResource('task-groups', V1TaskGroupController::class);
    Route::apiResource('monitor-tasks', V1MonitorTaskController::class);
    Route::apiResource('alert-rules', V1AlertRuleController::class);
    Route::apiResource('competitor-metrics', CompetitorMetricsController::class);
    
    Route::prefix('products/{product}')->group(function () {
        Route::apiResource('skus', V1ProductController::class)->except(['store', 'update', 'destroy']);
    });
}); 
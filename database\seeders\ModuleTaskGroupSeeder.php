<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\TaskGroup;
use App\Models\User;

class ModuleTaskGroupSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 获取第一个用户，如果没有用户则创建一个
        $user = User::first();
        if (!$user) {
            $user = User::create([
                'username' => 'admin',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'phone' => '13800138000',
                'is_active' => true,
            ]);
        }

        // 为每个模块创建默认任务分组
        $taskGroups = [
            [
                'name' => '默认渠道价格监测分组',
                'description' => '用于渠道价格监测的默认任务分组',
                'module_type' => 'channel_price',
                'color' => '#007bff',
                'sort_order' => 1,
            ],
            [
                'name' => '电商平台价格监测',
                'description' => '监测主要电商平台的价格变化',
                'module_type' => 'channel_price',
                'color' => '#28a745',
                'sort_order' => 2,
            ],
            [
                'name' => '默认竞品动态监测分组',
                'description' => '用于竞品动态监测的默认任务分组',
                'module_type' => 'competitor_dynamics',
                'color' => '#ffc107',
                'sort_order' => 3,
            ],
            [
                'name' => '主要竞争对手监测',
                'description' => '监测主要竞争对手的价格和促销策略',
                'module_type' => 'competitor_dynamics',
                'color' => '#fd7e14',
                'sort_order' => 4,
            ],
            [
                'name' => '默认相似同款查询分组',
                'description' => '用于相似同款查询的默认任务分组',
                'module_type' => 'similar_products',
                'color' => '#6f42c1',
                'sort_order' => 5,
            ],
            [
                'name' => '侵权商品监测',
                'description' => '监测可能存在侵权的相似商品',
                'module_type' => 'similar_products',
                'color' => '#e83e8c',
                'sort_order' => 6,
            ],
        ];

        foreach ($taskGroups as $groupData) {
            // 检查是否已存在相同的任务分组
            $existing = TaskGroup::where('user_id', $user->id)
                ->where('name', $groupData['name'])
                ->where('module_type', $groupData['module_type'])
                ->first();

            if (!$existing) {
                TaskGroup::create(array_merge($groupData, [
                    'user_id' => $user->id,
                    'is_active' => true,
                ]));
            }
        }

        $this->command->info('模块任务分组创建完成！');
    }
}

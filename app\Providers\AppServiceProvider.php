<?php

namespace App\Providers;

use App\Services\ConfigurationService;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;
use App\Http\View\Composers\NotificationComposer;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // 注册配置管理服务
        $this->app->singleton('configuration', function ($app) {
            return new ConfigurationService();
        });
        
        $this->app->singleton(ConfigurationService::class, function ($app) {
            return $app['configuration'];
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        View::composer('layouts.app', NotificationComposer::class);
    }
} 
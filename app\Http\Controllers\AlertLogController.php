<?php

namespace App\Http\Controllers;

use App\Models\AlertLog;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\View;

class AlertLogController extends Controller
{
    /**
     * 显示告警日志页面
     */
    public function indexPage(): View
    {
        return view('alert-logs.index');
    }

    /**
     * 获取告警日志列表
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = AlertLog::with(['user', 'rule', 'task', 'sku'])
                ->where('user_id', Auth::id());

            // 搜索功能
            if ($request->filled('search')) {
                $searchTerm = $request->search;
                $query->where(function ($q) use ($searchTerm) {
                    $q->where('alert_title', 'like', "%{$searchTerm}%")
                      ->orWhere('message', 'like', "%{$searchTerm}%");
                });
            }

            // 状态筛选
            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }

            // 严重程度筛选
            if ($request->filled('severity')) {
                $query->where('severity', $request->severity);
            }

            // 时间范围筛选
            if ($request->filled('date_from')) {
                $query->where('created_at', '>=', $request->date_from);
            }

            if ($request->filled('date_to')) {
                $query->where('created_at', '<=', $request->date_to);
            }

            // 分页
            $perPage = $request->get('per_page', 15);
            $alertLogs = $query->orderBy('created_at', 'desc')->paginate($perPage);

            return response()->json([
                'success' => true,
                'message' => '获取告警日志成功',
                'data' => $alertLogs
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取告警日志失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取单个告警日志详情
     */
    public function show($id): JsonResponse
    {
        try {
            $alertLog = AlertLog::with(['user', 'rule', 'task', 'sku'])
                ->where('user_id', Auth::id())
                ->findOrFail($id);

            return response()->json([
                'success' => true,
                'message' => '获取告警日志详情成功',
                'data' => $alertLog
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '告警日志不存在或获取失败',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * 标记告警为已读
     */
    public function markAsRead($id): JsonResponse
    {
        try {
            $alertLog = AlertLog::where('user_id', Auth::id())->findOrFail($id);
            $alertLog->markAsRead();

            return response()->json([
                'success' => true,
                'message' => '告警已标记为已读',
                'data' => $alertLog
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '标记已读失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 标记告警为已解决
     */
    public function markAsResolved($id): JsonResponse
    {
        try {
            $alertLog = AlertLog::where('user_id', Auth::id())->findOrFail($id);
            $alertLog->markAsResolved();

            return response()->json([
                'success' => true,
                'message' => '告警已标记为已解决',
                'data' => $alertLog
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '标记已解决失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 批量标记告警
     */
    public function batchUpdate(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'ids' => 'required|array',
                'ids.*' => 'required|integer|exists:alert_logs,id',
                'status' => 'required|in:read,resolved,ignored'
            ]);

            $alertLogs = AlertLog::whereIn('id', $validated['ids'])
                ->where('user_id', Auth::id())
                ->get();

            foreach ($alertLogs as $alertLog) {
                switch ($validated['status']) {
                    case 'read':
                        $alertLog->markAsRead();
                        break;
                    case 'resolved':
                        $alertLog->markAsResolved();
                        break;
                    case 'ignored':
                        $alertLog->markAsIgnored();
                        break;
                }
            }

            return response()->json([
                'success' => true,
                'message' => '批量更新告警状态成功',
                'updated_count' => $alertLogs->count()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '批量更新失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取告警统计
     */
    public function stats(): JsonResponse
    {
        try {
            $userId = Auth::id();
            $stats = AlertLog::getStatsForUser($userId);

            return response()->json([
                'success' => true,
                'message' => '获取告警统计成功',
                'data' => $stats
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取告警统计失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 删除告警日志
     */
    public function destroy($id): JsonResponse
    {
        try {
            $alertLog = AlertLog::where('user_id', Auth::id())->findOrFail($id);
            $alertLog->delete();

            return response()->json([
                'success' => true,
                'message' => '告警日志删除成功'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '删除告警日志失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 批量删除告警日志
     */
    public function batchDestroy(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'ids' => 'required|array',
                'ids.*' => 'required|integer|exists:alert_logs,id'
            ]);

            $deletedCount = AlertLog::whereIn('id', $validated['ids'])
                ->where('user_id', Auth::id())
                ->delete();

            return response()->json([
                'success' => true,
                'message' => '批量删除告警日志成功',
                'deleted_count' => $deletedCount
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '批量删除失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }
} 
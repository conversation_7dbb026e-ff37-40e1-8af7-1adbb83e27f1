<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据源表单测试</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select, textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin-top: 20px; padding: 15px; border-radius: 4px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
    </style>
</head>
<body>
    <h1>数据源创建功能测试</h1>
    
    <form id="testForm">
        <div class="form-group">
            <label for="name">数据源名称 *</label>
            <input type="text" id="name" name="name" value="测试API数据源" required>
        </div>
        
        <div class="form-group">
            <label for="platform">平台 *</label>
            <select id="platform" name="platform" required>
                <option value="淘宝">淘宝</option>
                <option value="京东">京东</option>
                <option value="天猫">天猫</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="description">描述</label>
            <textarea id="description" name="description" rows="3">这是一个用于测试的API数据源</textarea>
        </div>
        
        <div class="form-group">
            <label for="type">数据源类型 *</label>
            <select id="type" name="type" required>
                <option value="api">API接口</option>
                <option value="excel">Excel文件</option>
                <option value="csv">CSV文件</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="api_url">API地址 *</label>
            <input type="url" id="api_url" name="api_url" value="https://api.example.com/products" required>
        </div>
        
        <div class="form-group">
            <label for="api_method">请求方法</label>
            <select id="api_method" name="api_method">
                <option value="GET">GET</option>
                <option value="POST">POST</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="api_key">API密钥</label>
            <input type="password" id="api_key" name="api_key" value="test-api-key-123">
        </div>
        
        <div class="form-group">
            <label for="update_interval">更新频率（分钟）</label>
            <input type="number" id="update_interval" name="update_interval" value="60" min="1">
        </div>
        
        <div class="form-group">
            <label for="api_headers">请求头（JSON格式）</label>
            <textarea id="api_headers" name="api_headers" rows="3">{"Content-Type": "application/json", "User-Agent": "EcommerceTool/1.0"}</textarea>
        </div>
        
        <div class="form-group">
            <label>
                <input type="checkbox" name="auto_update" value="1" checked> 自动更新数据
            </label>
        </div>
        
        <div class="form-group">
            <label>
                <input type="checkbox" name="deduplication" value="1" checked> 自动去重
            </label>
        </div>
        
        <div class="form-group">
            <label>
                <input type="checkbox" name="data_validation" value="1" checked> 数据验证
            </label>
        </div>
        
        <div class="form-group">
            <label>
                <input type="checkbox" name="error_notification" value="1" checked> 错误通知
            </label>
        </div>
        
        <button type="submit">提交测试</button>
    </form>
    
    <div id="result"></div>
    
    <script>
        document.getElementById('testForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<p>正在提交...</p>';
            
            // 收集表单数据
            const formData = new FormData(this);
            
            // 添加CSRF token（需要从Laravel获取）
            try {
                // 首先获取CSRF token
                const csrfResponse = await fetch('/data-sources/create');
                const csrfText = await csrfResponse.text();
                const csrfMatch = csrfText.match(/name="_token" value="([^"]+)"/);
                
                if (csrfMatch) {
                    formData.append('_token', csrfMatch[1]);
                }
                
                // 提交表单数据
                const response = await fetch('/data-sources', {
                    method: 'POST',
                    body: formData,
                    credentials: 'same-origin'
                });
                
                if (response.ok) {
                    if (response.redirected) {
                        resultDiv.innerHTML = '<div class="success">✅ 数据源创建成功！页面将重定向到: ' + response.url + '</div>';
                    } else {
                        const result = await response.text();
                        resultDiv.innerHTML = '<div class="success">✅ 提交成功！<br><pre>' + result + '</pre></div>';
                    }
                } else {
                    const errorText = await response.text();
                    resultDiv.innerHTML = '<div class="error">❌ 提交失败 (状态码: ' + response.status + ')<br><pre>' + errorText + '</pre></div>';
                }
                
            } catch (error) {
                resultDiv.innerHTML = '<div class="error">❌ 网络错误: ' + error.message + '</div>';
            }
        });
    </script>
</body>
</html> 
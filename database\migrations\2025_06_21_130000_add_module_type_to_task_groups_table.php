<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('task_groups', function (Blueprint $table) {
            // 添加模块类型字段
            $table->enum('module_type', ['channel_price', 'competitor_dynamics', 'similar_products'])
                ->nullable()
                ->comment('模块类型：渠道价格监测、竞品动态监测、相似同款查询');
            
            // 添加索引
            $table->index(['module_type', 'user_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('task_groups', function (Blueprint $table) {
            $table->dropColumn('module_type');
        });
    }
};

@extends('layouts.app')

@section('title', '编辑数据源')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- 页面标题 -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">编辑数据源</h1>
                    <p class="text-muted mb-0">修改数据源配置信息</p>
                </div>
                <div>
                    <a href="{{ route('data-sources.index') }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-1"></i>返回列表
                    </a>
                    <a href="{{ route('data-sources.show', $dataSource->id) }}" class="btn btn-outline-info">
                        <i class="fas fa-eye me-1"></i>查看详情
                    </a>
                </div>
            </div>

            <!-- 错误提示 -->
            @if($errors->any())
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        @foreach($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <!-- 成功提示 -->
            @if(session('success'))
                <div class="alert alert-success">
                    {{ session('success') }}
                </div>
            @endif

            <!-- 编辑表单 -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-edit me-2"></i>编辑数据源信息
                    </h5>
                </div>
                <div class="card-body">
                    <form id="editDataSourceForm" method="POST" action="{{ route('data-sources.update', $dataSource->id) }}">
                        @csrf
                        @method('PUT')
                        
                        <!-- 基本信息 -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-muted border-bottom pb-2">基本信息</h6>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">数据源名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="{{ old('name', $dataSource->name) }}" required>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="platform" class="form-label">平台 <span class="text-danger">*</span></label>
                                <select class="form-select" id="platform" name="platform" required>
                                    <option value="">请选择平台</option>
                                    <option value="淘宝" {{ old('platform', $dataSource->platform) === '淘宝' ? 'selected' : '' }}>淘宝</option>
                                    <option value="京东" {{ old('platform', $dataSource->platform) === '京东' ? 'selected' : '' }}>京东</option>
                                    <option value="天猫" {{ old('platform', $dataSource->platform) === '天猫' ? 'selected' : '' }}>天猫</option>
                                    <option value="拼多多" {{ old('platform', $dataSource->platform) === '拼多多' ? 'selected' : '' }}>拼多多</option>
                                    <option value="其他" {{ old('platform', $dataSource->platform) === '其他' ? 'selected' : '' }}>其他</option>
                                </select>
                            </div>
                            
                            <div class="col-12 mb-3">
                                <label for="description" class="form-label">描述</label>
                                <textarea class="form-control" id="description" name="description" rows="3" 
                                          placeholder="可选：描述此数据源的用途">{{ old('description', $dataSource->description) }}</textarea>
                            </div>
                        </div>

                        <!-- 数据源类型 -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-muted border-bottom pb-2">数据源类型</h6>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="type" id="typeApi" value="api" 
                                                   {{ old('type', $dataSource->type) === 'api' ? 'checked' : '' }}>
                                            <label class="form-check-label" for="typeApi">
                                                <i class="fas fa-plug me-1"></i>API接口
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="type" id="typeExcel" value="excel"
                                                   {{ old('type', $dataSource->type) === 'excel' ? 'checked' : '' }}>
                                            <label class="form-check-label" for="typeExcel">
                                                <i class="fas fa-file-excel me-1"></i>Excel文件
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="type" id="typeCsv" value="csv"
                                                   {{ old('type', $dataSource->type) === 'csv' ? 'checked' : '' }}>
                                            <label class="form-check-label" for="typeCsv">
                                                <i class="fas fa-file-csv me-1"></i>CSV文件
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- API配置 -->
                        <div id="apiConfig" class="row mb-4" style="display: {{ old('type', $dataSource->type) === 'api' ? 'block' : 'none' }};">
                            <div class="col-12">
                                <h6 class="text-muted border-bottom pb-2">API配置</h6>
                            </div>
                            
                            <div class="col-md-8 mb-3">
                                <label for="api_url" class="form-label">API地址 <span class="text-danger">*</span></label>
                                <input type="url" class="form-control" id="api_url" name="api_url" 
                                       value="{{ old('api_url', $dataSource->config['api_url'] ?? '') }}"
                                       placeholder="https://api.example.com/products">
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="api_method" class="form-label">请求方法</label>
                                <select class="form-select" id="api_method" name="api_method">
                                    <option value="GET" {{ old('api_method', $dataSource->config['api_method'] ?? 'GET') === 'GET' ? 'selected' : '' }}>GET</option>
                                    <option value="POST" {{ old('api_method', $dataSource->config['api_method'] ?? 'GET') === 'POST' ? 'selected' : '' }}>POST</option>
                                </select>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="api_key" class="form-label">API密钥</label>
                                <input type="password" class="form-control" id="api_key" name="api_key" 
                                       value="{{ old('api_key', $dataSource->config['api_key'] ?? '') }}"
                                       placeholder="可选：API访问密钥">
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="update_interval" class="form-label">更新间隔（分钟）</label>
                                <input type="number" class="form-control" id="update_interval" name="update_interval" 
                                       value="{{ old('update_interval', $dataSource->config['update_interval'] ?? 60) }}" min="1">
                            </div>
                            
                            <div class="col-12 mb-3">
                                <label for="api_headers" class="form-label">请求头（JSON格式）</label>
                                <textarea class="form-control" id="api_headers" name="api_headers" rows="3" 
                                          placeholder='{"Authorization": "Bearer token", "Content-Type": "application/json"}'>{{ old('api_headers', json_encode($dataSource->config['api_headers'] ?? [], JSON_PRETTY_PRINT)) }}</textarea>
                                <div class="form-text">可选：自定义HTTP请求头，请使用有效的JSON格式</div>
                            </div>
                        </div>

                        <!-- 文件配置 -->
                        <div id="fileConfig" class="row mb-4" style="display: {{ in_array(old('type', $dataSource->type), ['excel', 'csv']) ? 'block' : 'none' }};">
                            <div class="col-12">
                                <h6 class="text-muted border-bottom pb-2">文件配置</h6>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="file_encoding" class="form-label">文件编码</label>
                                <select class="form-select" id="file_encoding" name="file_encoding">
                                    <option value="utf-8" {{ old('file_encoding', $dataSource->config['file_encoding'] ?? 'utf-8') === 'utf-8' ? 'selected' : '' }}>UTF-8</option>
                                    <option value="gbk" {{ old('file_encoding', $dataSource->config['file_encoding'] ?? 'utf-8') === 'gbk' ? 'selected' : '' }}>GBK</option>
                                    <option value="gb2312" {{ old('file_encoding', $dataSource->config['file_encoding'] ?? 'utf-8') === 'gb2312' ? 'selected' : '' }}>GB2312</option>
                                </select>
                            </div>
                            
                            <div id="csvConfig" class="col-md-6 mb-3" style="display: {{ old('type', $dataSource->type) === 'csv' ? 'block' : 'none' }};">
                                <label for="delimiter" class="form-label">分隔符</label>
                                <select class="form-select" id="delimiter" name="delimiter">
                                    <option value="," {{ old('delimiter', $dataSource->config['delimiter'] ?? ',') === ',' ? 'selected' : '' }}>逗号 (,)</option>
                                    <option value=";" {{ old('delimiter', $dataSource->config['delimiter'] ?? ',') === ';' ? 'selected' : '' }}>分号 (;)</option>
                                    <option value="\t" {{ old('delimiter', $dataSource->config['delimiter'] ?? ',') === "\t" ? 'selected' : '' }}>制表符 (\t)</option>
                                    <option value="|" {{ old('delimiter', $dataSource->config['delimiter'] ?? ',') === '|' ? 'selected' : '' }}>竖线 (|)</option>
                                </select>
                            </div>
                            
                            <div class="col-12 mb-3">
                                <label for="column_mapping" class="form-label">列映射配置（JSON格式）</label>
                                <textarea class="form-control" id="column_mapping" name="column_mapping" rows="3" 
                                          placeholder='{"商品名称": "product_name", "价格": "price", "库存": "stock"}'>{{ old('column_mapping', json_encode($dataSource->config['column_mapping'] ?? [], JSON_PRETTY_PRINT)) }}</textarea>
                                <div class="form-text">可选：定义文件列名与系统字段的映射关系</div>
                            </div>
                        </div>

                        <!-- 高级选项 -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-muted border-bottom pb-2">高级选项</h6>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="auto_update" name="auto_update" 
                                                   {{ old('auto_update', $dataSource->options['auto_update'] ?? false) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="auto_update">自动更新</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="deduplication" name="deduplication"
                                                   {{ old('deduplication', $dataSource->options['deduplication'] ?? false) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="deduplication">去重处理</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="data_validation" name="data_validation"
                                                   {{ old('data_validation', $dataSource->options['data_validation'] ?? false) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="data_validation">数据验证</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="error_notification" name="error_notification"
                                                   {{ old('error_notification', $dataSource->options['error_notification'] ?? false) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="error_notification">错误通知</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 提交按钮 -->
                        <div class="row">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-save me-1"></i>保存更改
                                </button>
                                <a href="{{ route('data-sources.show', $dataSource->id) }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i>取消
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 表单提交处理 - 显示加载状态
document.getElementById('editDataSourceForm').addEventListener('submit', function(e) {
    // 验证必填字段
    const name = document.getElementById('name').value.trim();
    const platform = document.getElementById('platform').value;
    const type = document.querySelector('input[name="type"]:checked').value;
    
    if (!name || !platform) {
        alert('请填写完整的基本信息');
        e.preventDefault();
        return false;
    }
    
    // 如果是API类型，验证API URL
    if (type === 'api') {
        const apiUrl = document.getElementById('api_url').value.trim();
        if (!apiUrl) {
            alert('API类型必须填写API地址');
            e.preventDefault();
            return false;
        }
    }
    
    // 验证JSON格式
    const apiHeaders = document.getElementById('api_headers').value.trim();
    if (apiHeaders) {
        try {
            JSON.parse(apiHeaders);
        } catch (e) {
            alert('API请求头格式错误，请使用有效的JSON格式');
            e.preventDefault();
            return false;
        }
    }
    
    const columnMapping = document.getElementById('column_mapping').value.trim();
    if (columnMapping) {
        try {
            JSON.parse(columnMapping);
        } catch (e) {
            alert('列映射配置格式错误，请使用有效的JSON格式');
            e.preventDefault();
            return false;
        }
    }
    
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>保存中...';
    submitBtn.disabled = true;
    
    // 允许表单正常提交，不阻止默认行为
});

// 类型切换处理
document.querySelectorAll('input[name="type"]').forEach(radio => {
    radio.addEventListener('change', function() {
        const apiConfig = document.getElementById('apiConfig');
        const fileConfig = document.getElementById('fileConfig');
        const csvConfig = document.getElementById('csvConfig');
        
        if (this.value === 'api') {
            apiConfig.style.display = 'block';
            fileConfig.style.display = 'none';
        } else {
            apiConfig.style.display = 'none';
            fileConfig.style.display = 'block';
            
            if (this.value === 'csv') {
                csvConfig.style.display = 'block';
            } else {
                csvConfig.style.display = 'none';
            }
        }
    });
});
</script>
@endsection 
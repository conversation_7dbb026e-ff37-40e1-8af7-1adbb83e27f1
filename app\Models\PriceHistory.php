<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class PriceHistory extends Model
{
    use HasFactory;

    protected $table = 'price_history';

    protected $fillable = [
        'sku_id',
        'task_id',
        'price',
        'sub_price',
        'official_guide_price',
        'promotion_deviation_rate',
        'channel_deviation_rate',
        'deviation_calculated_at',
        'calculation_status',
        'quantity',
        'promotion_info',
        'sales',
        'comment_count',
        'timestamp',
        'price_change',
        'price_change_rate',
        'additional_data',
        'data_source',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'sub_price' => 'decimal:2',
        'official_guide_price' => 'decimal:2',
        'promotion_deviation_rate' => 'decimal:4',
        'channel_deviation_rate' => 'decimal:4',
        'deviation_calculated_at' => 'datetime',
        'price_change' => 'decimal:2',
        'price_change_rate' => 'decimal:2',
        'quantity' => 'integer',
        'sales' => 'integer',
        'comment_count' => 'integer',
        'promotion_info' => 'array',
        'additional_data' => 'array',
        'timestamp' => 'datetime',
    ];

    /**
     * 所属SKU
     */
    public function sku(): BelongsTo
    {
        return $this->belongsTo(ProductSku::class, 'sku_id');
    }

    /**
     * 通过SKU获取产品信息
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    /**
     * 通过SKU关系获取产品信息（推荐使用）
     */
    public function productViaSku()
    {
        return $this->hasOneThrough(
            Product::class,
            ProductSku::class,
            'id', // SKU表的外键 
            'id', // Product表的主键
            'sku_id', // PriceHistory表的SKU外键
            'product_id' // SKU表的Product外键
        );
    }

    /**
     * 获取价格变化趋势文本
     */
    public function getPriceTrendAttribute(): string
    {
        if ($this->price_change > 0) {
            return '上涨';
        } elseif ($this->price_change < 0) {
            return '下降';
        }
        return '无变化';
    }

    /**
     * 获取价格变化方向图标
     */
    public function getPriceIconAttribute(): string
    {
        if ($this->price_change > 0) {
            return '📈'; // 上涨
        } elseif ($this->price_change < 0) {
            return '📉'; // 下降
        }
        return '➡️'; // 无变化
    }

    /**
     * 检查是否有显著价格变化
     */
    public function hasSignificantPriceChange(float $threshold = 5.0): bool
    {
        return abs($this->price_change_rate) >= $threshold;
    }

    /**
     * 检查是否有促销活动
     */
    public function hasPromotion(): bool
    {
        return !empty($this->promotion_info);
    }

    /**
     * 获取格式化的价格变化信息
     */
    public function getFormattedPriceChangeAttribute(): string
    {
        $change = $this->price_change;
        $rate = $this->price_change_rate;
        
        if ($change == 0) {
            return '无变化';
        }
        
        $sign = $change > 0 ? '+' : '';
        return "{$sign}¥{$change} ({$sign}{$rate}%)";
    }

    /**
     * 按SKU筛选
     */
    public function scopeForSku($query, $skuId)
    {
        return $query->where('sku_id', $skuId);
    }

    /**
     * 按时间范围筛选
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('timestamp', [
            Carbon::parse($startDate)->startOfDay(),
            Carbon::parse($endDate)->endOfDay()
        ]);
    }

    /**
     * 最近的记录
     */
    public function scopeRecent($query, $days = 7)
    {
        return $query->where('timestamp', '>=', Carbon::now()->subDays($days));
    }

    /**
     * 有价格变化的记录
     */
    public function scopeWithPriceChange($query)
    {
        return $query->where('price_change', '!=', 0);
    }

    /**
     * 显著价格变化的记录
     */
    public function scopeSignificantChange($query, $threshold = 5.0)
    {
        return $query->where(function($q) use ($threshold) {
            $q->where('price_change_rate', '>=', $threshold)
              ->orWhere('price_change_rate', '<=', -$threshold);
        });
    }

    /**
     * 按数据源筛选
     */
    public function scopeFromSource($query, $source)
    {
        return $query->where('data_source', $source);
    }

    /**
     * 获取SKU的价格趋势数据
     */
    public static function getPriceTrend($skuId, $days = 30)
    {
        return static::forSku($skuId)
                    ->recent($days)
                    ->orderBy('timestamp')
                    ->select(['timestamp', 'price', 'price_change', 'price_change_rate'])
                    ->get();
    }

    /**
     * 计算平均价格
     */
    public static function getAveragePrice($skuId, $days = 30)
    {
        return static::forSku($skuId)
                    ->recent($days)
                    ->avg('price');
    }

    /**
     * 获取最低价格
     */
    public static function getLowestPrice($skuId, $days = 30)
    {
        return static::forSku($skuId)
                    ->recent($days)
                    ->min('price');
    }

    /**
     * 获取最高价格
     */
    public static function getHighestPrice($skuId, $days = 30)
    {
        return static::forSku($skuId)
                    ->recent($days)
                    ->max('price');
    }

    /**
     * 检查是否已计算偏差率
     */
    public function hasCalculatedDeviations(): bool
    {
        return $this->calculation_status === 'calculated';
    }

    /**
     * 检查是否需要重新计算偏差率
     */
    public function needsDeviationRecalculation(): bool
    {
        return $this->calculation_status === 'pending' || $this->calculation_status === 'failed';
    }

    /**
     * 获取格式化的促销偏差率
     */
    public function getFormattedPromotionDeviationAttribute(): ?string
    {
        if ($this->promotion_deviation_rate === null) {
            return null;
        }
        
        $rate = number_format($this->promotion_deviation_rate, 2);
        return "{$rate}%";
    }

    /**
     * 获取格式化的渠道偏差率
     */
    public function getFormattedChannelDeviationAttribute(): ?string
    {
        if ($this->channel_deviation_rate === null) {
            return null;
        }
        
        $rate = number_format($this->channel_deviation_rate, 2);
        return "{$rate}%";
    }

    /**
     * 按计算状态筛选
     */
    public function scopeByCalculationStatus($query, $status)
    {
        return $query->where('calculation_status', $status);
    }

    /**
     * 需要计算偏差率的记录
     */
    public function scopeNeedsCalculation($query)
    {
        return $query->whereIn('calculation_status', ['pending', 'failed']);
    }

    /**
     * 已计算偏差率的记录
     */
    public function scopeCalculated($query)
    {
        return $query->where('calculation_status', 'calculated');
    }

    /**
     * 有显著促销偏差的记录
     */
    public function scopeSignificantPromotionDeviation($query, $threshold = 10.0)
    {
        return $query->where('promotion_deviation_rate', '>=', $threshold);
    }

    /**
     * 有显著渠道偏差的记录
     */
    public function scopeSignificantChannelDeviation($query, $threshold = 15.0)
    {
        return $query->where('channel_deviation_rate', '>=', $threshold);
    }

    /**
     * 获取偏差率统计信息
     */
    public static function getDeviationStats($skuId = null, $days = 30)
    {
        $query = static::calculated();
        
        if ($skuId) {
            $query->forSku($skuId);
        }
        
        $query->recent($days);
        
        $records = $query->get();
        
        $promotionRates = $records->whereNotNull('promotion_deviation_rate')
                                ->pluck('promotion_deviation_rate')
                                ->toArray();
        
        $channelRates = $records->whereNotNull('channel_deviation_rate')
                              ->pluck('channel_deviation_rate')
                              ->toArray();
        
        return [
            'promotion_deviation' => [
                'count' => count($promotionRates),
                'avg' => count($promotionRates) > 0 ? round(array_sum($promotionRates) / count($promotionRates), 2) : null,
                'min' => count($promotionRates) > 0 ? min($promotionRates) : null,
                'max' => count($promotionRates) > 0 ? max($promotionRates) : null,
            ],
            'channel_deviation' => [
                'count' => count($channelRates),
                'avg' => count($channelRates) > 0 ? round(array_sum($channelRates) / count($channelRates), 2) : null,
                'min' => count($channelRates) > 0 ? min($channelRates) : null,
                'max' => count($channelRates) > 0 ? max($channelRates) : null,
            ]
        ];
    }
}

<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\ProductSku;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class ProductController extends Controller
{
    /**
     * 获取产品列表
     */
    public function index(Request $request): JsonResponse
    {
        $query = Product::with(['skus', 'category']);

        // 搜索功能
        if ($request->filled('search')) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('name', 'like', "%{$searchTerm}%")
                  ->orWhere('brand', 'like', "%{$searchTerm}%")
                  ->orWhere('description', 'like', "%{$searchTerm}%");
            });
        }

        // 平台筛选
        if ($request->filled('platform')) {
            $query->where('platform', $request->platform);
        }

        // 状态筛选
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // 分页
        $perPage = $request->get('per_page', 15);
        $products = $query->orderBy('created_at', 'desc')->paginate($perPage);

        return response()->json([
            'success' => true,
            'message' => '获取产品列表成功',
            'data' => $products
        ]);
    }

    /**
     * 获取单个产品详情
     */
    public function show($id): JsonResponse
    {
        try {
            $product = Product::with(['skus', 'category', 'monitorTasks'])->findOrFail($id);

            return response()->json([
                'success' => true,
                'message' => '获取产品详情成功',
                'data' => $product
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '产品不存在或获取失败',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * 创建新产品
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'brand' => 'nullable|string|max:100',
                'platform' => 'required|string|max:50',
                'category_id' => 'nullable|exists:categories,id',
                'description' => 'nullable|string',
                'image_url' => 'nullable|url',
                'status' => 'in:active,inactive,draft'
            ]);

            $validated['status'] = $validated['status'] ?? 'active';
            
            $product = Product::create($validated);

            return response()->json([
                'success' => true,
                'message' => '产品创建成功',
                'data' => $product->load(['skus', 'category'])
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '产品创建失败',
                'error' => $e->getMessage()
            ], 422);
        }
    }

    /**
     * 更新产品
     */
    public function update(Request $request, $id): JsonResponse
    {
        try {
            $product = Product::findOrFail($id);

            $validated = $request->validate([
                'name' => 'sometimes|required|string|max:255',
                'brand' => 'nullable|string|max:100',
                'platform' => 'sometimes|required|string|max:50',
                'category_id' => 'nullable|exists:categories,id',
                'description' => 'nullable|string',
                'image_url' => 'nullable|url',
                'status' => 'in:active,inactive,draft'
            ]);

            $product->update($validated);

            return response()->json([
                'success' => true,
                'message' => '产品更新成功',
                'data' => $product->load(['skus', 'category'])
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '产品更新失败',
                'error' => $e->getMessage()
            ], 422);
        }
    }

    /**
     * 删除产品
     */
    public function destroy($id): JsonResponse
    {
        try {
            $product = Product::findOrFail($id);
            
            // 检查是否有关联的监控任务
            if ($product->monitorTasks()->count() > 0) {
                return response()->json([
                    'success' => false,
                    'message' => '该产品存在关联的监控任务，无法删除'
                ], 400);
            }

            $product->delete();

            return response()->json([
                'success' => true,
                'message' => '产品删除成功'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '产品删除失败',
                'error' => $e->getMessage()
            ], 422);
        }
    }

    /**
     * 获取产品的SKU列表
     */
    public function skus($id): JsonResponse
    {
        try {
            $product = Product::findOrFail($id);
            $skus = $product->skus()->select('id', 'sku_code', 'title', 'price', 'official_guide_price', 'status')->get();

            return response()->json([
                'success' => true,
                'message' => '获取产品SKU成功',
                'data' => $skus
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取产品SKU失败',
                'error' => $e->getMessage()
            ], 404);
        }
    }
} 
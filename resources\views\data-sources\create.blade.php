@extends('layouts.app')

@section('title', '添加数据源')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">首页</a></li>
    <li class="breadcrumb-item"><a href="{{ route('data-sources.index') }}">数据源管理</a></li>
    <li class="breadcrumb-item active">添加数据源</li>
@endsection

@section('page-title', '添加数据源')

@section('content')
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-plus me-2"></i>新建数据源
                </h6>
            </div>
            <div class="card-body">

                {{-- Session Messages & Validation Errors --}}
                @if (session('success'))
                    <div class="alert alert-success">
                        {{ session('success') }}
                    </div>
                @endif
                @if (session('error'))
                    <div class="alert alert-danger">
                        {{ session('error') }}
                    </div>
                @endif
                @if ($errors->any())
                    <div class="alert alert-danger pb-0">
                        <ul class="mb-3">
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif

                <form id="createDataSourceForm" method="POST" action="{{ route('data-sources.store') }}">
                    @csrf
                    
                    <!-- 基本信息 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2">基本信息</h6>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">数据源名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" required>
                                <div class="form-text">请输入易于识别的数据源名称</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="platform" class="form-label">平台 <span class="text-danger">*</span></label>
                                <select class="form-select" id="platform" name="platform" required>
                                    <option value="">请选择平台</option>
                                    <option value="淘宝">淘宝</option>
                                    <option value="京东">京东</option>
                                    <option value="天猫">天猫</option>
                                    <option value="拼多多">拼多多</option>
                                    <option value="其他">其他</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="mb-3">
                                <label for="description" class="form-label">描述</label>
                                <textarea class="form-control" id="description" name="description" rows="3" placeholder="请输入数据源的详细描述"></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- 数据源类型 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2">数据源类型</h6>
                        </div>
                        <div class="col-12">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card border-primary h-100">
                                        <div class="card-body text-center">
                                            <input type="radio" class="form-check-input" id="typeManual" name="type" value="manual" checked>
                                            <label for="typeManual" class="form-check-label d-block mt-2">
                                                <i class="fas fa-keyboard fa-2x text-primary mb-2"></i>
                                                <h6>手动输入</h6>
                                                <small class="text-muted">手动输入单个或批量商品URL</small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card border-success h-100">
                                        <div class="card-body text-center">
                                            <input type="radio" class="form-check-input" id="typeExcel" name="type" value="excel">
                                            <label for="typeExcel" class="form-check-label d-block mt-2">
                                                <i class="fas fa-file-excel fa-2x text-success mb-2"></i>
                                                <h6>Excel文件</h6>
                                                <small class="text-muted">上传Excel文件导入数据</small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 手动输入配置 -->
                    <div id="manualConfig" class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2">商品信息输入</h6>
                        </div>
                        <div class="col-12">
                            <div class="mb-3">
                                <label for="productUrls" class="form-label">商品URL <span class="text-danger">*</span></label>
                                <textarea class="form-control" id="productUrls" name="product_urls" rows="8" 
                                    placeholder="请输入商品URL，一行一个：&#10;https://item.taobao.com/item.htm?id=123456&#10;https://item.jd.com/123456.html&#10;https://mobile.yangkeduo.com/goods.html?goods_id=123456" required></textarea>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    支持淘宝、天猫、京东、拼多多等平台的商品URL，每行一个URL
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="autoMonitor" class="form-label">监控设置</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="autoMonitor" name="auto_monitor" value="1">
                                    <label class="form-check-label" for="autoMonitor">
                                        自动启用价格监控
                                    </label>
                                </div>
                                <div class="form-text">勾选后系统将自动监控这些商品的价格变化</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="monitorInterval" class="form-label">监控频率（分钟）</label>
                                <input type="number" class="form-control" id="monitorInterval" name="monitor_interval" value="60" min="1" max="1440">
                                <div class="form-text">设置价格检查的时间间隔</div>
                            </div>
                        </div>
                    </div>

                    <!-- 数据处理选项 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2">数据处理选项</h6>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="autoUpdate" name="auto_update" checked>
                                <label class="form-check-label" for="autoUpdate">
                                    自动更新数据
                                </label>
                            </div>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="deduplication" name="deduplication" checked>
                                <label class="form-check-label" for="deduplication">
                                    自动去重
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="dataValidation" name="data_validation" checked>
                                <label class="form-check-label" for="dataValidation">
                                    数据验证
                                </label>
                            </div>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="errorNotification" name="error_notification" checked>
                                <label class="form-check-label" for="errorNotification">
                                    错误通知
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- 提交按钮 -->
                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex justify-content-end gap-2">
                                <a href="{{ route('data-sources.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-times me-1"></i>取消
                                </a>
                                <button type="button" class="btn btn-info" onclick="testConnection()">
                                    <i class="fas fa-plug me-1"></i>测试连接
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>保存数据源
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 监听数据源类型变化
    const typeRadios = document.querySelectorAll('input[name="type"]');
    typeRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            toggleConfigSections(this.value);
        });
    });
    
    // 初始化显示手动输入配置
    toggleConfigSections('manual');
});

// 切换配置区域显示
function toggleConfigSections(type) {
    const manualConfig = document.getElementById('manualConfig');
    const fileConfig = document.getElementById('fileConfig');
    
    if (type === 'manual') {
        manualConfig.style.display = 'block';
        fileConfig.style.display = 'none';
        
        // 设置手动输入字段为必填
        document.getElementById('productUrls').required = true;
    } else {
        manualConfig.style.display = 'none';
        fileConfig.style.display = 'block';
        
        // 移除手动输入字段必填
        document.getElementById('productUrls').required = false;
    }
}

// 测试连接
function testConnection() {
    const type = document.querySelector('input[name="type"]:checked').value;
    
    if (type === 'manual') {
        const productUrls = document.getElementById('productUrls').value.trim();
        if (!productUrls) {
            alert('请先输入商品URL');
            return;
        }
        
        // 验证URL格式
        const urls = productUrls.split('\n').filter(url => url.trim() !== '');
        let validCount = 0;
        let invalidUrls = [];
        
        urls.forEach((url, index) => {
            const trimmedUrl = url.trim();
            if (isValidUrl(trimmedUrl)) {
                validCount++;
            } else {
                invalidUrls.push(`第${index + 1}行: ${trimmedUrl}`);
            }
        });
        
        if (invalidUrls.length > 0) {
            alert(`发现 ${invalidUrls.length} 个无效URL：\n${invalidUrls.slice(0, 5).join('\n')}${invalidUrls.length > 5 ? '\n...' : ''}`);
        } else {
            alert(`验证成功！共 ${validCount} 个有效的商品URL`);
        }
    } else {
        alert('Excel文件类型数据源无需测试连接');
    }
}

// 验证URL格式
function isValidUrl(string) {
    try {
        const url = new URL(string);
        const supportedDomains = ['taobao.com', 'tmall.com', 'jd.com', 'pinduoduo.com', 'yangkeduo.com'];
        return supportedDomains.some(domain => url.hostname.includes(domain));
    } catch (_) {
        return false;
    }
}

// 表单提交处理 - 显示加载状态
document.getElementById('createDataSourceForm').addEventListener('submit', function(e) {
    // 验证必填字段
    const name = document.getElementById('name').value.trim();
    const platform = document.getElementById('platform').value;
    const type = document.querySelector('input[name="type"]:checked').value;
    
    if (!name || !platform) {
        alert('请填写完整的基本信息');
        e.preventDefault();
        return false;
    }
    
    // 如果是手动输入类型，验证商品URL
    if (type === 'manual') {
        const productUrls = document.getElementById('productUrls').value.trim();
        if (!productUrls) {
            alert('请输入至少一个商品URL');
            e.preventDefault();
            return false;
        }
        
        // 验证URL格式
        const urls = productUrls.split('\n').filter(url => url.trim() !== '');
        if (urls.length === 0) {
            alert('请输入有效的商品URL');
            e.preventDefault();
            return false;
        }
    }
    
    // 显示加载状态
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>保存中...';
    submitBtn.disabled = true;
    
    // 允许表单正常提交
    return true;
});
</script>
@endsection

<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;
use App\Http\Controllers\DataSourceController;
use App\Models\User;
use App\Models\DataSource;

// 启动Laravel应用
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "测试数据源创建功能...\n\n";

// 创建测试用户
$user = User::first();
if (!$user) {
    echo "错误：没有找到用户，请先创建用户\n";
    exit(1);
}

echo "使用用户: {$user->name} (ID: {$user->id})\n\n";

// 模拟认证
auth()->login($user);

// 创建测试请求数据
$testData = [
    'name' => '测试API数据源',
    'platform' => '淘宝',
    'description' => '这是一个测试API数据源',
    'type' => 'api',
    'api_url' => 'https://api.example.com/products',
    'api_method' => 'GET',
    'api_key' => 'test-api-key',
    'update_interval' => 60,
    'api_headers' => '{"Content-Type": "application/json"}',
    'auto_update' => true,
    'deduplication' => true,
    'data_validation' => true,
    'error_notification' => true,
];

echo "测试数据:\n";
print_r($testData);
echo "\n";

// 创建请求对象
$request = new Request();
$request->merge($testData);

// 创建控制器实例
$controller = new DataSourceController();

try {
    echo "调用store方法...\n";
    $response = $controller->store($request);
    
    echo "响应类型: " . get_class($response) . "\n";
    
    if ($response instanceof \Illuminate\Http\RedirectResponse) {
        echo "重定向响应 - 目标URL: " . $response->getTargetUrl() . "\n";
        
        // 检查session消息
        $session = $response->getSession();
        if ($session && $session->has('success')) {
            echo "成功消息: " . $session->get('success') . "\n";
        }
        if ($session && $session->has('error')) {
            echo "错误消息: " . $session->get('error') . "\n";
        }
    }
    
    // 检查数据库中是否创建了记录
    $dataSource = DataSource::where('name', $testData['name'])->first();
    if ($dataSource) {
        echo "\n✅ 数据源创建成功!\n";
        echo "ID: {$dataSource->id}\n";
        echo "名称: {$dataSource->name}\n";
        echo "类型: {$dataSource->type}\n";
        echo "状态: {$dataSource->status}\n";
        echo "配置: " . json_encode($dataSource->config, JSON_UNESCAPED_UNICODE) . "\n";
        echo "选项: " . json_encode($dataSource->options, JSON_UNESCAPED_UNICODE) . "\n";
    } else {
        echo "\n❌ 数据源创建失败 - 数据库中没有找到记录\n";
    }
    
} catch (Exception $e) {
    echo "❌ 捕获异常: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
}

echo "\n测试完成。\n"; 
<?php

// 测试数据源创建功能
require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\DB;
use App\Models\DataSource;
use App\Models\User;

// 启动Laravel应用
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

try {
    echo "开始测试数据源创建功能...\n";
    
    // 1. 检查数据库连接
    echo "1. 检查数据库连接...\n";
    DB::connection()->getPdo();
    echo "   ✓ 数据库连接正常\n";
    
    // 2. 检查data_sources表结构
    echo "2. 检查data_sources表结构...\n";
    $columns = DB::select("DESCRIBE data_sources");
    $columnNames = array_column($columns, 'Field');
    
    $requiredColumns = ['name', 'description', 'type', 'config', 'options'];
    $missingColumns = array_diff($requiredColumns, $columnNames);
    
    if (empty($missingColumns)) {
        echo "   ✓ 所有必需字段都存在\n";
    } else {
        echo "   ✗ 缺少字段: " . implode(', ', $missingColumns) . "\n";
        echo "   需要运行迁移: php artisan migrate\n";
    }
    
    // 3. 检查status字段的枚举值
    echo "3. 检查status字段的枚举值...\n";
    $statusColumn = collect($columns)->firstWhere('Field', 'status');
    if ($statusColumn) {
        echo "   Status字段类型: " . $statusColumn->Type . "\n";
        if (strpos($statusColumn->Type, 'pending') !== false) {
            echo "   ✓ status字段包含'pending'值\n";
        } else {
            echo "   ✗ status字段不包含'pending'值\n";
        }
    }
    
    // 4. 获取测试用户
    echo "4. 获取测试用户...\n";
    $user = User::first();
    if ($user) {
        echo "   ✓ 找到用户: {$user->name} (ID: {$user->id})\n";
    } else {
        echo "   ✗ 没有找到用户\n";
        exit(1);
    }
    
    // 5. 尝试创建数据源
    echo "5. 尝试创建测试数据源...\n";
    
    $testData = [
        'name' => '测试数据源',
        'platform' => '淘宝',
        'description' => '这是一个测试数据源',
        'type' => 'api',
        'api_url' => 'https://api.example.com/products',
        'api_method' => 'GET',
        'api_key' => 'test-api-key',
        'update_interval' => 60,
        'api_headers' => '{"Content-Type": "application/json"}',
        'auto_update' => true,
        'deduplication' => true,
        'data_validation' => true,
        'error_notification' => true,
    ];
    
    // 验证JSON字段
    $apiHeaders = json_decode($testData['api_headers'], true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        echo "❌ API请求头JSON格式错误\n";
    } else {
        echo "✅ API请求头JSON格式正确\n";
    }
    
    // 验证必填字段
    $requiredFields = ['name', 'platform', 'type'];
    foreach ($requiredFields as $field) {
        if (empty($testData[$field])) {
            echo "❌ 必填字段 {$field} 为空\n";
        } else {
            echo "✅ 必填字段 {$field} 已填写: {$testData[$field]}\n";
        }
    }
    
    // 验证API字段
    if ($testData['type'] === 'api') {
        if (empty($testData['api_url'])) {
            echo "❌ API类型需要填写API地址\n";
        } else {
            echo "✅ API地址已填写: {$testData['api_url']}\n";
        }
    }
    
    echo "\n数据源配置:\n";
    $config = [
        'api_url' => $testData['api_url'] ?? null,
        'api_method' => $testData['api_method'] ?? 'GET',
        'api_key' => $testData['api_key'] ?? null,
        'update_interval' => $testData['update_interval'] ?? 60,
        'api_headers' => $apiHeaders,
    ];
    print_r($config);
    
    echo "\n数据处理选项:\n";
    $options = [
        'auto_update' => $testData['auto_update'] ?? false,
        'deduplication' => $testData['deduplication'] ?? false,
        'data_validation' => $testData['data_validation'] ?? false,
        'error_notification' => $testData['error_notification'] ?? false,
    ];
    print_r($options);
    
    try {
        $dataSource = DataSource::create($testData);
        echo "   ✓ 数据源创建成功! ID: {$dataSource->id}\n";
        
        // 验证数据
        echo "6. 验证创建的数据...\n";
        $created = DataSource::find($dataSource->id);
        echo "   名称: {$created->name}\n";
        echo "   平台: {$created->platform}\n";
        echo "   类型: {$created->type}\n";
        echo "   状态: {$created->status}\n";
        echo "   配置: " . json_encode($created->config) . "\n";
        
        // 清理测试数据
        echo "7. 清理测试数据...\n";
        $created->delete();
        echo "   ✓ 测试数据已清理\n";
        
    } catch (Exception $e) {
        echo "   ✗ 数据源创建失败: " . $e->getMessage() . "\n";
        echo "   错误详情: " . $e->getTraceAsString() . "\n";
    }
    
    echo "\n测试完成!\n";
    
} catch (Exception $e) {
    echo "测试失败: " . $e->getMessage() . "\n";
    echo "错误详情: " . $e->getTraceAsString() . "\n";
} 
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class DataSource extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'import_log_id',
        'product_id',
        'source_type',
        'platform',
        'source_url',
        'source_id',
        'normalized_url',
        'status',
        'error_message',
        'extracted_data',
        'processed_at',
        'last_fetched_at',
        'fetch_attempts',
        'is_active',
        'auto_monitor',
        'metadata',
        // 新增配置字段
        'name',
        'description',
        'type',
        'config',
        'options'
    ];

    protected $casts = [
        'extracted_data' => 'array',
        'metadata' => 'array',
        'config' => 'array',
        'options' => 'array',
        'processed_at' => 'datetime',
        'last_fetched_at' => 'datetime',
        'is_active' => 'boolean',
        'auto_monitor' => 'boolean'
    ];

    // 关联关系
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function importLog(): BelongsTo
    {
        return $this->belongsTo(ImportLog::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    // 作用域
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByPlatform($query, $platform)
    {
        return $query->where('platform', $platform);
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    // 静态方法：URL解析和验证
    public static function parseUrl($url)
    {
        $url = trim($url);
        
        // 标准化URL（移除多余参数）
        $normalizedUrl = self::normalizeUrl($url);
        
        // 提取平台和商品ID
        $platform = self::extractPlatform($url);
        $sourceId = self::extractSourceId($url, $platform);
        
        if (!$platform || !$sourceId) {
            return null;
        }
        
        return [
            'platform' => $platform,
            'source_id' => $sourceId,
            'source_url' => $url,
            'normalized_url' => $normalizedUrl,
            'source_type' => 'url'
        ];
    }

    public static function validateSourceId($sourceId, $platform)
    {
        // 根据平台验证商品ID格式
        switch ($platform) {
            case 'taobao':
            case 'tmall':
                return preg_match('/^\d{10,}$/', $sourceId);
            case 'jd':
                return preg_match('/^\d{7,}$/', $sourceId);
            case 'pdd':
                return preg_match('/^\d{8,}$/', $sourceId);
            default:
                return strlen($sourceId) > 0;
        }
    }

    protected static function normalizeUrl($url)
    {
        // 移除不必要的参数，保留核心URL
        $parsedUrl = parse_url($url);
        if (!$parsedUrl) {
            return $url;
        }

        $baseUrl = $parsedUrl['scheme'] . '://' . $parsedUrl['host'];
        if (isset($parsedUrl['path'])) {
            $baseUrl .= $parsedUrl['path'];
        }

        // 只保留必要的查询参数
        if (isset($parsedUrl['query'])) {
            parse_str($parsedUrl['query'], $queryParams);
            $keepParams = ['id', 'item_id', 'goods_id', 'sku_id'];
            $filteredParams = array_intersect_key($queryParams, array_flip($keepParams));
            
            if (!empty($filteredParams)) {
                $baseUrl .= '?' . http_build_query($filteredParams);
            }
        }

        return $baseUrl;
    }

    protected static function extractPlatform($url)
    {
        $host = parse_url($url, PHP_URL_HOST);
        if (!$host) {
            return null;
        }

        // 平台识别规则
        if (strpos($host, 'taobao.com') !== false) {
            return 'taobao';
        } elseif (strpos($host, 'tmall.com') !== false) {
            return 'tmall';
        } elseif (strpos($host, 'jd.com') !== false) {
            return 'jd';
        } elseif (strpos($host, 'yangkeduo.com') !== false || strpos($host, 'pinduoduo.com') !== false) {
            return 'pdd';
        }

        return null;
    }

    protected static function extractSourceId($url, $platform)
    {
        switch ($platform) {
            case 'taobao':
            case 'tmall':
                // 匹配item.htm?id=123456 或 /item/123456.htm
                if (preg_match('/[?&]id=(\d+)/', $url, $matches)) {
                    return $matches[1];
                }
                if (preg_match('/\/item\/(\d+)\.htm/', $url, $matches)) {
                    return $matches[1];
                }
                break;
                
            case 'jd':
                // 匹配product/123456.html
                if (preg_match('/\/product\/(\d+)\.html/', $url, $matches)) {
                    return $matches[1];
                }
                break;
                
            case 'pdd':
                // 匹配goods_id=123456
                if (preg_match('/[?&]goods_id=(\d+)/', $url, $matches)) {
                    return $matches[1];
                }
                break;
        }

        return null;
    }

    // 业务逻辑方法
    public function markAsProcessing()
    {
        $this->update(['status' => 'processing']);
    }

    public function markAsCompleted($extractedData = null)
    {
        $this->update([
            'status' => 'completed',
            'processed_at' => Carbon::now(),
            'extracted_data' => $extractedData
        ]);
    }

    public function markAsFailed($errorMessage)
    {
        $this->increment('fetch_attempts');
        $this->update([
            'status' => 'failed',
            'error_message' => $errorMessage,
            'processed_at' => Carbon::now()
        ]);
    }

    public function markAsDuplicate()
    {
        $this->update(['status' => 'duplicate']);
    }

    public function canRetry()
    {
        return $this->fetch_attempts < 3 && $this->status === 'failed';
    }

    public function createProduct()
    {
        if (!$this->extracted_data || $this->product_id) {
            return false;
        }

        $productData = $this->extracted_data;
        $product = Product::create([
            'title' => $productData['title'] ?? '',
            'platform' => $this->platform,
            'platform_product_id' => $this->source_id,
            'product_url' => $this->normalized_url,
            'current_price' => $productData['price'] ?? 0,
            'original_price' => $productData['original_price'] ?? 0,
            'sales_count' => $productData['sales_count'] ?? 0,
            'rating' => $productData['rating'] ?? 0,
            'review_count' => $productData['review_count'] ?? 0,
            'status' => 'active'
        ]);

        $this->update(['product_id' => $product->id]);
        
        return $product;
    }

    public function getStatusTextAttribute()
    {
        $statusMap = [
            'pending' => '待处理',
            'processing' => '处理中',
            'completed' => '已完成',
            'failed' => '失败',
            'duplicate' => '重复'
        ];

        return $statusMap[$this->status] ?? $this->status;
    }

    public function getPlatformTextAttribute()
    {
        $platformMap = [
            'taobao' => '淘宝',
            'tmall' => '天猫',
            'jd' => '京东',
            'pdd' => '拼多多'
        ];

        return $platformMap[$this->platform] ?? $this->platform;
    }
}

<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title', '电商市场动态监测系统') - {{ config('app.name', 'Laravel') }}</title>

    <!-- 本地化CSS资源 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- UX增强样式 -->
    <link href="{{ asset('css/ux-enhancements.css') }}" rel="stylesheet">

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <style>
        :root {
            --sidebar-width: 250px;
            --topbar-height: 60px;
            --primary-color: #4e73df;
            --secondary-color: #2c3e50;
            --sidebar-bg: #2c3e50;
            --sidebar-active: #4e73df;
        }

        body {
            background-color: #f8f9fc;
            font-family: 'Nunito', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
        }

        .wrapper {
            display: flex;
            width: 100%;
        }

        /* 顶部导航栏 */
        .topbar {
            position: fixed;
            top: 0;
            left: var(--sidebar-width);
            right: 0;
            height: var(--topbar-height);
            background: var(--primary-color);
            color: white;
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 1.5rem;
            transition: all 0.3s;
        }

        .topbar.sidebar-collapsed {
            left: 0;
        }

        .topbar-brand {
            font-size: 1.2rem;
            font-weight: 600;
        }

        .topbar-nav {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .user-dropdown .dropdown-toggle {
            background: none;
            border: none;
            color: white;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .user-dropdown .dropdown-toggle:hover {
            background: rgba(255,255,255,0.1);
        }

        /* 侧边栏 */
        #sidebar {
            width: var(--sidebar-width);
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            z-index: 999;
            background: var(--sidebar-bg);
            color: #fff;
            transition: all 0.3s;
            overflow-y: auto;
        }

        #sidebar.collapsed {
            margin-left: calc(-1 * var(--sidebar-width));
        }

        .sidebar-header {
            padding: 1rem;
            background: #34495e;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .sidebar-header h3 {
            color: #fff;
            margin: 0;
            font-size: 1.1rem;
        }

        #sidebarCollapse {
            display: none;
        }

        #sidebar .components {
            padding: 0;
        }

        #sidebar ul li {
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        #sidebar ul li a {
            padding: 0.75rem 1rem;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            transition: all 0.3s;
        }

        #sidebar ul li a:hover {
            color: #fff;
            background: rgba(255,255,255,0.1);
        }

        #sidebar ul li.active > a {
            color: #fff;
            background: var(--sidebar-active);
        }

        #sidebar ul li ul li a {
            padding-left: 2.5rem;
            font-size: 0.85rem;
        }

        .nav-text {
            margin-left: 0.5rem;
        }

        /* 主内容区域 */
        #content {
            width: calc(100% - var(--sidebar-width));
            margin-left: var(--sidebar-width);
            margin-top: var(--topbar-height);
            padding: 1.5rem;
            min-height: calc(100vh - var(--topbar-height));
            transition: all 0.3s;
        }

        #content.sidebar-collapsed {
            width: 100%;
            margin-left: 0;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            :root {
                --sidebar-width: 0px;
            }

            #sidebar {
                margin-left: calc(-1 * 250px);
                z-index: 1050; /* 确保侧边栏在最上层 */
                transition: margin-left 0.3s ease;
                position: fixed;
                top: 0;
                left: 0;
                height: 100vh;
                overflow-y: auto;
            }

            #sidebar.show {
                margin-left: 0;
                box-shadow: 2px 0 5px rgba(0,0,0,0.1);
            }

            .topbar {
                left: 0;
                width: 100%;
                z-index: 1040;
            }

            #content {
                width: 100%;
                margin-left: 0;
            }

            #sidebarCollapse {
                display: inline-block;
            }

            /* 添加遮罩层效果 */
            body.sidebar-open::before {
                content: '';
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0,0,0,0.5);
                z-index: 1049;
                opacity: 0;
                visibility: hidden;
                transition: opacity 0.3s ease, visibility 0.3s ease;
            }

            body.sidebar-open.sidebar-show::before {
                opacity: 1;
                visibility: visible;
            }
        }

        /* 卡片样式 */
        .card {
            border: none;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        }

        .card-header {
            background-color: #f8f9fc;
            border-bottom: 1px solid #e3e6f0;
        }

        /* 表格样式 */
        .table {
            color: #5a5c69;
        }

        .table thead th {
            border-top: none;
            border-bottom: 1px solid #e3e6f0;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.8rem;
            color: #6e707e;
        }

        /* 按钮样式 */
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: #2e59d9;
            border-color: #2e59d9;
        }
    </style>
</head>
<body class="font-sans antialiased">
    <div class="wrapper">
        <!-- 侧边栏 -->
        @include('layouts.navigation')

        <!-- 顶部导航栏 -->
        <nav class="topbar" id="topbar">
            <div class="topbar-brand">
                <button class="btn btn-link text-white d-md-none" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <span class="d-none d-md-inline">商品监控平台</span>
            </div>

            <div class="topbar-nav">
                <!-- 通知 -->
                <div class="dropdown">
                    <button class="btn btn-link text-white dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-bell"></i>
                        @if(isset($unreadNotificationsCount) && $unreadNotificationsCount > 0)
                            <span class="badge bg-danger badge-counter">{{ $unreadNotificationsCount > 9 ? '9+' : $unreadNotificationsCount }}</span>
                        @endif
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><h6 class="dropdown-header">通知中心</h6></li>
                        <li><a class="dropdown-item" href="{{ route('alert-logs.index') }}">价格异常警报</a></li>
                        <li><a class="dropdown-item" href="{{ route('alert-logs.index') }}">数据更新完成</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="{{ route('alert-logs.index') }}">查看所有通知</a></li>
                    </ul>
                </div>

                <!-- 用户菜单 -->
                <div class="dropdown user-dropdown">
                    <button class="btn dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <img src="{{ asset('images/default-avatar.svg') }}" class="rounded-circle me-2" width="32" height="32" alt="用户头像">
                        <span>{{ auth()->user()->name ?? '管理员' }}</span>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="{{ route('profile.index') }}">
                            <i class="fas fa-user me-2"></i>个人中心
                        </a></li>
                        <li><a class="dropdown-item" href="{{ route('profile.preferences') }}">
                            <i class="fas fa-cog me-2"></i>设置
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="event.preventDefault(); document.getElementById('logout-form-top').submit();">
                            <i class="fas fa-sign-out-alt me-2"></i>退出登录
                        </a></li>
                    </ul>
                    <form id="logout-form-top" action="{{ route('logout') }}" method="POST" class="d-none">
                        @csrf
                    </form>
                </div>
            </div>
        </nav>

        <!-- 主内容区域 -->
        <div id="content">
            <!-- 面包屑导航 -->
            @if(!empty(trim($__env->yieldContent('breadcrumb'))))
            <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                    @yield('breadcrumb')
                </ol>
            </nav>
            @endif

            <!-- 页面标题 -->
            @if(!empty(trim($__env->yieldContent('page-title'))))
            <div class="d-sm-flex align-items-center justify-content-between mb-4">
                <h1 class="h3 mb-0 text-gray-800">@yield('page-title')</h1>
                @yield('page-actions')
            </div>
            @endif

            <!-- 消息提示 -->
            @if(session('success'))
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            @endif

            @if(session('error'))
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>{{ session('error') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            @endif

            @if(session('warning'))
            <div class="alert alert-warning alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>{{ session('warning') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            @endif

            <!-- 页面内容 -->
            <main id="main-content" role="main" aria-label="主要内容区域">
                @yield('content')
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- UX增强脚本 -->
    <script src="{{ asset('js/ux-enhancements.js') }}"></script>

    <!-- 自定义JS -->
    <script>
        // 侧边栏切换
        document.addEventListener('DOMContentLoaded', function() {
            // 确保Bootstrap已加载
            if (typeof bootstrap === 'undefined') {
                console.error('Bootstrap JavaScript未正确加载');
                return;
            }

            const sidebarToggle = document.getElementById('sidebarToggle');
            const sidebar = document.getElementById('sidebar');
            const content = document.getElementById('content');
            const topbar = document.getElementById('topbar');

            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('show');
                    document.body.classList.toggle('sidebar-open');

                    // 延迟添加show类以实现动画效果
                    if (sidebar.classList.contains('show')) {
                        setTimeout(() => {
                            document.body.classList.add('sidebar-show');
                        }, 10);
                    } else {
                        document.body.classList.remove('sidebar-show');
                    }
                });
            }

            // 点击遮罩层关闭侧边栏
            document.addEventListener('click', function(e) {
                if (window.innerWidth <= 768 &&
                    sidebar.classList.contains('show') &&
                    !sidebar.contains(e.target) &&
                    !sidebarToggle.contains(e.target)) {
                    sidebar.classList.remove('show');
                    document.body.classList.remove('sidebar-open', 'sidebar-show');
                }
            });

            // 桌面端侧边栏折叠
            const sidebarCollapse = document.getElementById('sidebarCollapse');
            if (sidebarCollapse) {
                sidebarCollapse.addEventListener('click', function() {
                    sidebar.classList.toggle('collapsed');
                    content.classList.toggle('sidebar-collapsed');
                    topbar.classList.toggle('sidebar-collapsed');
                });
            }

            // 手动初始化下拉菜单（确保它们工作）
            const dropdownElements = document.querySelectorAll('[data-bs-toggle="dropdown"]');
            dropdownElements.forEach(function(element) {
                try {
                    // 确保没有重复初始化
                    if (!element.classList.contains('dropdown-initialized')) {
                        new bootstrap.Dropdown(element);
                        element.classList.add('dropdown-initialized');
                    }
                } catch (e) {
                    console.warn('Failed to initialize dropdown:', e);
                }
            });

            // 为导航中的折叠菜单初始化
            const collapseElements = document.querySelectorAll('[data-bs-toggle="collapse"]');
            collapseElements.forEach(function(element) {
                try {
                    if (!element.classList.contains('collapse-initialized')) {
                        new bootstrap.Collapse(element.getAttribute('data-bs-target') || element.getAttribute('href'), {
                            toggle: false
                        });
                        element.classList.add('collapse-initialized');
                    }
                } catch (e) {
                    console.warn('Failed to initialize collapse:', e);
                }
            });

            // 自动隐藏消息提示
            setTimeout(function() {
                const alerts = document.querySelectorAll('.alert');
                alerts.forEach(function(alert) {
                    if (bootstrap.Alert) {
                        try {
                            const bsAlert = new bootstrap.Alert(alert);
                            bsAlert.close();
                        } catch (e) {
                            console.warn('Failed to close alert:', e);
                        }
                    }
                });
            }, 5000);

            // 修复通知按钮和用户菜单
            initializeNotificationDropdown();
            initializeUserDropdown();
        });

        // 通知下拉菜单初始化
        function initializeNotificationDropdown() {
            const notificationToggle = document.querySelector('.topbar-nav .dropdown [data-bs-toggle="dropdown"]');
            if (notificationToggle && !notificationToggle.classList.contains('notification-initialized')) {
                try {
                    const dropdown = new bootstrap.Dropdown(notificationToggle);
                    notificationToggle.classList.add('notification-initialized');
                    
                    // 添加点击事件监听器
                    notificationToggle.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        dropdown.toggle();
                    });
                } catch (e) {
                    console.warn('Failed to initialize notification dropdown:', e);
                }
            }
        }

        // 用户下拉菜单初始化
        function initializeUserDropdown() {
            const userToggle = document.querySelector('.user-dropdown [data-bs-toggle="dropdown"]');
            if (userToggle && !userToggle.classList.contains('user-dropdown-initialized')) {
                try {
                    const dropdown = new bootstrap.Dropdown(userToggle);
                    userToggle.classList.add('user-dropdown-initialized');
                    
                    // 添加点击事件监听器
                    userToggle.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        dropdown.toggle();
                    });
                } catch (e) {
                    console.warn('Failed to initialize user dropdown:', e);
                }
            }
        }
    </script>
    @yield('scripts')
    @stack('scripts')
</body>
</html> 
@extends('layouts.app')

@section('title', '竞品分析')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">首页</a></li>
    <li class="breadcrumb-item active">竞品分析</li>
@endsection

@section('page-title', '竞品分析')

@section('page-actions')
    <div class="btn-group">
        <button type="button" class="btn btn-primary" onclick="showAnalysisReport()">
            <i class="fas fa-chart-bar me-1"></i>分析报告
        </button>
        <button type="button" class="btn btn-success" onclick="exportAnalysis()">
            <i class="fas fa-download me-1"></i>导出分析
        </button>
        <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#helpModal">
            <i class="fas fa-question-circle me-1"></i>使用帮助
        </button>
    </div>
@endsection

@section('content')
<div class="container-fluid">
    <!-- 快速统计卡片 -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                今日搜索次数
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="todaySearchCount">0</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-search fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                发现竞品数量
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="competitorCount">0</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                平均价格差异
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="avgPriceDiff">0%</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-percentage fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                价格优势产品
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="advantageCount">0</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-trophy fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 搜索表单 -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-search"></i> 搜索条件
            </h5>
        </div>
        <div class="card-body">
            <form id="searchForm">
                <div class="row">
                    <!-- 关键词搜索 -->
                    <div class="col-md-6 mb-3">
                        <label for="keywords" class="form-label">关键词</label>
                        <input type="text" class="form-control" id="keywords" placeholder="输入产品关键词，用空格分隔">
                        <small class="form-text text-muted">例如：手机 苹果 iPhone</small>
                    </div>

                    <!-- 排除词 -->
                    <div class="col-md-6 mb-3">
                        <label for="excludeWords" class="form-label">排除词</label>
                        <input type="text" class="form-control" id="excludeWords" placeholder="输入要排除的词，用空格分隔">
                        <small class="form-text text-muted">例如：二手 翻新</small>
                    </div>

                    <!-- 分类路径 -->
                    <div class="col-md-6 mb-3">
                        <label for="categories" class="form-label">分类路径</label>
                        <input type="text" class="form-control" id="categories" placeholder="输入分类路径，用逗号分隔">
                        <small class="form-text text-muted">例如：数码产品/手机/智能手机</small>
                    </div>

                    <!-- 搜索逻辑 -->
                    <div class="col-md-6 mb-3">
                        <label for="operator" class="form-label">关键词逻辑</label>
                        <select class="form-select" id="operator">
                            <option value="OR">OR（包含任一关键词）</option>
                            <option value="AND">AND（包含所有关键词）</option>
                        </select>
                    </div>

                    <!-- 高级筛选 -->
                    <div class="col-12 mb-3">
                        <button type="button" class="btn btn-outline-secondary" data-bs-toggle="collapse" data-bs-target="#advancedFilters">
                            <i class="fas fa-filter"></i> 高级筛选
                        </button>
                    </div>

                    <!-- 高级筛选面板 -->
                    <div class="collapse" id="advancedFilters">
                        <div class="card card-body">
                            <div class="row">
                                <!-- 平台筛选 -->
                                <div class="col-md-3 mb-3">
                                    <label for="platform" class="form-label">平台</label>
                                    <select class="form-select" id="platform">
                                        <option value="">全部平台</option>
                                        <option value="taobao">淘宝</option>
                                        <option value="tmall">天猫</option>
                                        <option value="jd">京东</option>
                                        <option value="pdd">拼多多</option>
                                    </select>
                                </div>

                                <!-- 价格范围 -->
                                <div class="col-md-3 mb-3">
                                    <label for="minPrice" class="form-label">最低价格</label>
                                    <input type="number" class="form-control" id="minPrice" placeholder="0" min="0" step="0.01">
                                </div>

                                <div class="col-md-3 mb-3">
                                    <label for="maxPrice" class="form-label">最高价格</label>
                                    <input type="number" class="form-control" id="maxPrice" placeholder="不限" min="0" step="0.01">
                                </div>

                                <!-- 销量筛选 -->
                                <div class="col-md-3 mb-3">
                                    <label for="minSales" class="form-label">最低销量</label>
                                    <input type="number" class="form-control" id="minSales" placeholder="0" min="0">
                                </div>

                                <!-- 评分筛选 -->
                                <div class="col-md-3 mb-3">
                                    <label for="minRating" class="form-label">最低评分</label>
                                    <select class="form-select" id="minRating">
                                        <option value="">不限</option>
                                        <option value="3">3分以上</option>
                                        <option value="4">4分以上</option>
                                        <option value="4.5">4.5分以上</option>
                                    </select>
                                </div>

                                <!-- 结果数量 -->
                                <div class="col-md-3 mb-3">
                                    <label for="limit" class="form-label">结果数量</label>
                                    <select class="form-select" id="limit">
                                        <option value="50">50</option>
                                        <option value="100" selected>100</option>
                                        <option value="200">200</option>
                                        <option value="500">500</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-search"></i> 搜索
                        </button>
                        <button type="button" class="btn btn-outline-secondary me-2" id="clearForm">
                            <i class="fas fa-times"></i> 清空
                        </button>
                        <button type="button" class="btn btn-outline-info" id="saveSearch">
                            <i class="fas fa-save"></i> 保存搜索
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 搜索结果 -->
    <div class="card" id="resultsCard" style="display: none;">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="fas fa-list"></i> 搜索结果
            </h5>
            <div>
                <button type="button" class="btn btn-outline-success btn-sm" id="exportResults">
                    <i class="fas fa-download"></i> 导出结果
                </button>
            </div>
        </div>
        <div class="card-body">
            <!-- 搜索统计 -->
            <div class="row mb-3" id="searchStats">
                <div class="col-md-3">
                    <div class="card bg-light">
                        <div class="card-body text-center">
                            <h5 class="card-title" id="totalResults">0</h5>
                            <p class="card-text">总结果数</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-light">
                        <div class="card-body text-center">
                            <h5 class="card-title" id="avgPrice">¥0</h5>
                            <p class="card-text">平均价格</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-light">
                        <div class="card-body text-center">
                            <h5 class="card-title" id="totalSales">0</h5>
                            <p class="card-text">总销量</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-light">
                        <div class="card-body text-center">
                            <h5 class="card-title" id="avgRating">0</h5>
                            <p class="card-text">平均评分</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 产品比较工具栏 -->
            <div class="d-flex justify-content-between align-items-center mb-3" id="compareToolbar" style="display: none !important;">
                <div>
                    <span class="badge bg-primary" id="compareCount">0</span> 个产品已选择
                </div>
                <div>
                    <button type="button" class="btn btn-outline-primary btn-sm me-2" id="compareSelected">
                        <i class="fas fa-balance-scale"></i> 比较选中
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-sm" id="clearSelection">
                        <i class="fas fa-times"></i> 清空选择
                    </button>
                </div>
            </div>

            <!-- 结果列表 -->
            <div class="table-responsive">
                <table class="table table-striped" id="resultsTable">
                    <thead>
                        <tr>
                            <th width="30">
                                <input type="checkbox" class="form-check-input" id="selectAll" title="全选/取消全选">
                            </th>
                            <th width="50">序号</th>
                            <th width="300">产品标题</th>
                            <th width="120">分类</th>
                            <th width="80">平台</th>
                            <th width="120">店铺</th>
                            <th width="100" data-sort="price">价格 <i class="fas fa-sort"></i></th>
                            <th width="80" data-sort="sales">销量 <i class="fas fa-sort"></i></th>
                            <th width="80" data-sort="rating">评分 <i class="fas fa-sort"></i></th>
                            <th width="120">操作</th>
                        </tr>
                    </thead>
                    <tbody id="resultsTableBody">
                        <!-- 搜索结果将在这里显示 -->
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <nav aria-label="搜索结果分页" id="pagination" style="display: none;">
                <ul class="pagination justify-content-center">
                    <!-- 分页链接将在这里生成 -->
                </ul>
            </nav>
        </div>
    </div>

    <!-- 加载中指示器 -->
    <div class="text-center" id="loadingIndicator" style="display: none;">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">搜索中...</span>
        </div>
        <p class="mt-2">正在搜索竞争对手产品...</p>
    </div>
</div>

<!-- 使用帮助模态框 -->
<div class="modal fade" id="helpModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">使用帮助</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <h6>搜索技巧：</h6>
                <ul>
                    <li><strong>关键词搜索</strong>：输入产品相关的关键词，用空格分隔多个词</li>
                    <li><strong>排除词</strong>：输入不希望出现在结果中的词语</li>
                    <li><strong>分类路径</strong>：输入具体的分类路径来缩小搜索范围</li>
                    <li><strong>搜索逻辑</strong>：选择OR包含任一关键词，选择AND包含所有关键词</li>
                </ul>
                
                <h6>高级筛选：</h6>
                <ul>
                    <li><strong>平台筛选</strong>：选择特定的电商平台</li>
                    <li><strong>价格范围</strong>：设置价格上下限</li>
                    <li><strong>销量筛选</strong>：设置最低销量要求</li>
                    <li><strong>评分筛选</strong>：设置最低评分要求</li>
                </ul>

                <h6>结果分析：</h6>
                <ul>
                    <li>查看搜索统计了解市场概况</li>
                    <li>点击产品标题查看详细信息</li>
                    <li>使用导出功能保存搜索结果</li>
                </ul>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 分析报告模态框 -->
<div class="modal fade" id="analysisReportModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-chart-bar me-2"></i>竞品分析报告
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <!-- 价格分布图 -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="m-0">价格分布分析</h6>
                            </div>
                            <div class="card-body">
                                <canvas id="priceDistributionChart" height="200"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- 平台分布图 -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="m-0">平台分布分析</h6>
                            </div>
                            <div class="card-body">
                                <canvas id="platformDistributionChart" height="200"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- 价格趋势图 -->
                    <div class="col-12 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="m-0">价格趋势分析</h6>
                            </div>
                            <div class="card-body">
                                <canvas id="priceTrendChart" height="100"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- 竞争力分析表格 -->
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="m-0">竞争力分析</h6>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>指标</th>
                                                <th>我方产品</th>
                                                <th>竞品平均</th>
                                                <th>竞争优势</th>
                                                <th>建议</th>
                                            </tr>
                                        </thead>
                                        <tbody id="competitiveAnalysisTable">
                                            <!-- 数据将通过JavaScript填充 -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="exportAnalysisReport()">
                    <i class="fas fa-download me-1"></i>导出报告
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 导出模态框 -->
<div class="modal fade" id="exportModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">导出搜索结果</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="exportForm">
                    <div class="mb-3">
                        <label for="exportFormat" class="form-label">导出格式</label>
                        <select class="form-select" id="exportFormat" required>
                            <option value="csv">CSV格式</option>
                            <option value="json">JSON格式</option>
                            <option value="excel">Excel格式</option>
                        </select>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="includeImages" checked>
                        <label class="form-check-label" for="includeImages">
                            包含产品图片链接
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmExport">确认导出</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.table th {
    background-color: #f8f9fa;
    border-top: none;
}

.product-title {
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.product-title:hover {
    white-space: normal;
    overflow: visible;
}

.price-highlight {
    color: #e74c3c;
    font-weight: bold;
}

.rating-stars {
    color: #f39c12;
}

.platform-badge {
    font-size: 0.875em;
}

.search-stats .card {
    transition: transform 0.2s;
}

.search-stats .card:hover {
    transform: translateY(-2px);
}

#loadingIndicator {
    padding: 3rem 0;
}

.collapse {
    transition: all 0.3s ease;
}

.btn-group-sm > .btn, .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}
</style>
@endpush

@push('styles')
<!-- DataTables CSS -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.bootstrap5.min.css">
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/select/1.7.0/css/select.bootstrap5.min.css">
@endpush

@push('scripts')
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
<!-- DataTables JS -->
<script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.bootstrap5.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/select/1.7.0/js/dataTables.select.min.js"></script>
<!-- API客户端 -->
<script src="{{ asset('js/api-client.js') }}"></script>
<script src="{{ asset('js/competitors.js') }}"></script>
<script>
// 确保全局函数可用
window.exportAnalysis = function() {
    if (window.competitorSearch && typeof window.competitorSearch.exportResults === 'function') {
        window.competitorSearch.showExportModal();
    } else {
        console.warn('导出功能暂不可用');
        alert('导出功能暂不可用，请稍后再试');
    }
};

window.showAnalysisReport = function() {
    if (window.competitorSearch && typeof window.competitorSearch.showAnalysisReport === 'function') {
        window.competitorSearch.showAnalysisReport();
    } else {
        console.warn('分析报告功能暂不可用');
        alert('分析报告功能暂不可用，请稍后再试');
    }
};

window.exportAnalysisReport = function() {
    if (window.competitorSearch && typeof window.competitorSearch.exportAnalysisReport === 'function') {
        window.competitorSearch.exportAnalysisReport();
    } else {
        console.warn('导出分析报告功能暂不可用');
        alert('导出分析报告功能暂不可用，请稍后再试');
    }
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 确保 competitorSearch 实例可用
    if (typeof CompetitorSearch !== 'undefined') {
        window.competitorSearch = new CompetitorSearch();
    }
});
</script>
@endpush 
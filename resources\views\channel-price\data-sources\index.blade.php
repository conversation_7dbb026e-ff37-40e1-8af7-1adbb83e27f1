@extends('layouts.app')

@section('title', '渠道价格监测 - 数据源管理')

@section('content')
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">渠道价格监测 - 数据源管理</h1>
            <p class="text-muted">管理渠道价格监测的数据源，支持API接入和Excel导入</p>
        </div>
        <div>
            <a href="{{ route('channel-price.data-sources.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>添加数据源
            </a>
            <a href="{{ route('channel-price.data-sources.excel-create') }}" class="btn btn-success">
                <i class="fas fa-file-excel me-2"></i>Excel导入
            </a>
        </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('channel-price.data-sources.index') }}">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label for="search" class="form-label">搜索</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ request('search') }}" placeholder="搜索数据源名称、URL...">
                    </div>
                    <div class="col-md-2">
                        <label for="status" class="form-label">状态</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">全部状态</option>
                            <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>启用</option>
                            <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>禁用</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="task_group_id" class="form-label">任务分组</label>
                        <select class="form-select" id="task_group_id" name="task_group_id">
                            <option value="">全部分组</option>
                            @foreach($taskGroups as $group)
                                <option value="{{ $group->id }}" {{ request('task_group_id') == $group->id ? 'selected' : '' }}>
                                    {{ $group->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-search me-2"></i>搜索
                            </button>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <a href="{{ route('channel-price.data-sources.index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-undo me-2"></i>重置
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 数据源列表 -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">数据源列表 ({{ $dataSources->total() }})</h5>
        </div>
        <div class="card-body p-0">
            @if($dataSources->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>数据源名称</th>
                                <th>API地址</th>
                                <th>任务分组</th>
                                <th>官方指导价</th>
                                <th>同步频率</th>
                                <th>同步状态</th>
                                <th>最后同步</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($dataSources as $dataSource)
                                <tr>
                                    <td>
                                        <div class="fw-bold">{{ $dataSource->name }}</div>
                                        @if($dataSource->description)
                                            <small class="text-muted">{{ Str::limit($dataSource->description, 50) }}</small>
                                        @endif
                                    </td>
                                    <td>
                                        @if($dataSource->api_url)
                                            <small class="text-muted">{{ Str::limit($dataSource->api_url, 40) }}</small>
                                        @else
                                            <span class="text-muted">-</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($dataSource->taskGroup)
                                            <span class="badge bg-info">{{ $dataSource->taskGroup->name }}</span>
                                        @else
                                            <span class="text-muted">-</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($dataSource->official_guide_price)
                                            <span class="text-success">¥{{ number_format($dataSource->official_guide_price, 2) }}</span>
                                        @else
                                            <span class="text-muted">-</span>
                                        @endif
                                    </td>
                                    <td>
                                        @php
                                            $frequencyMap = [
                                                'manual' => '手动',
                                                'hourly' => '每小时',
                                                'daily' => '每天',
                                                'weekly' => '每周'
                                            ];
                                        @endphp
                                        <span class="badge bg-secondary">{{ $frequencyMap[$dataSource->sync_frequency] ?? $dataSource->sync_frequency }}</span>
                                    </td>
                                    <td>
                                        @php
                                            $statusMap = [
                                                'pending' => ['class' => 'warning', 'text' => '待同步'],
                                                'syncing' => ['class' => 'info', 'text' => '同步中'],
                                                'success' => ['class' => 'success', 'text' => '成功'],
                                                'failed' => ['class' => 'danger', 'text' => '失败']
                                            ];
                                            $statusInfo = $statusMap[$dataSource->sync_status] ?? ['class' => 'secondary', 'text' => $dataSource->sync_status];
                                        @endphp
                                        <span class="badge bg-{{ $statusInfo['class'] }}">{{ $statusInfo['text'] }}</span>
                                    </td>
                                    <td>
                                        @if($dataSource->last_sync_at)
                                            <small>{{ $dataSource->last_sync_at->format('m-d H:i') }}</small>
                                        @else
                                            <span class="text-muted">从未同步</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($dataSource->status === 'active')
                                            <span class="badge bg-success">启用</span>
                                        @else
                                            <span class="badge bg-secondary">禁用</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ route('channel-price.data-sources.show', $dataSource) }}" 
                                               class="btn btn-outline-primary" title="查看详情">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('channel-price.data-sources.edit', $dataSource) }}" 
                                               class="btn btn-outline-warning" title="编辑">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-outline-info" 
                                                    onclick="syncDataSource({{ $dataSource->id }})" title="同步数据">
                                                <i class="fas fa-sync"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-danger" 
                                                    onclick="deleteDataSource({{ $dataSource->id }})" title="删除">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-database fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">暂无数据源</h5>
                    <p class="text-muted">点击上方按钮添加第一个渠道价格监测数据源</p>
                    <a href="{{ route('channel-price.data-sources.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>添加数据源
                    </a>
                </div>
            @endif
        </div>
        
        @if($dataSources->hasPages())
            <div class="card-footer">
                {{ $dataSources->links() }}
            </div>
        @endif
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除这个数据源吗？此操作不可撤销。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function syncDataSource(id) {
    if (confirm('确定要同步这个数据源吗？')) {
        fetch(`/channel-price/data-sources/${id}/sync`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('同步失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('同步失败，请重试');
        });
    }
}

function deleteDataSource(id) {
    document.getElementById('deleteForm').action = `/channel-price/data-sources/${id}`;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
@endpush

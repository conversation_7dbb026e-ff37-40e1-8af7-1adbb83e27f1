@extends('layouts.app')

@section('title', '添加商品')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">首页</a></li>
    <li class="breadcrumb-item"><a href="{{ route('products.index') }}">商品监控</a></li>
    <li class="breadcrumb-item active">添加商品</li>
@endsection

@section('page-title', '添加商品')

@section('content')
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-plus me-2"></i>新增商品监控
                </h6>
            </div>
            <div class="card-body">

                {{-- Session Messages & Validation Errors --}}
                @if (session('success'))
                    <div class="alert alert-success">
                        {{ session('success') }}
                    </div>
                @endif
                @if (session('error'))
                    <div class="alert alert-danger">
                        {{ session('error') }}
                    </div>
                @endif
                @if ($errors->any())
                    <div class="alert alert-danger pb-0">
                        <ul class="mb-3">
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif

                <!-- 添加方式选择 -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h6 class="text-primary border-bottom pb-2">添加方式</h6>
                    </div>
                    <div class="col-12">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card border-primary h-100">
                                    <div class="card-body text-center">
                                        <input type="radio" class="form-check-input" id="addTypeSingle" name="add_type" value="single" checked>
                                        <label for="addTypeSingle" class="form-check-label d-block mt-2">
                                            <i class="fas fa-plus fa-2x text-primary mb-2"></i>
                                            <h6>单个添加</h6>
                                            <small class="text-muted">添加单个商品，填写详细信息</small>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card border-success h-100">
                                    <div class="card-body text-center">
                                        <input type="radio" class="form-check-input" id="addTypeBatch" name="add_type" value="batch">
                                        <label for="addTypeBatch" class="form-check-label d-block mt-2">
                                            <i class="fas fa-list fa-2x text-success mb-2"></i>
                                            <h6>批量添加</h6>
                                            <small class="text-muted">批量输入多个商品URL</small>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 单个添加表单 -->
                <form id="singleAddForm" method="POST" action="{{ route('products.store') }}">
                    @csrf
                    
                    <!-- 基本信息 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2">商品信息</h6>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">商品名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" value="{{ old('name') }}" required>
                                <div class="form-text">请输入商品的完整名称</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="platform" class="form-label">电商平台 <span class="text-danger">*</span></label>
                                <select class="form-select" id="platform" name="platform" required>
                                    <option value="">请选择平台</option>
                                    <option value="淘宝" {{ old('platform') == '淘宝' ? 'selected' : '' }}>淘宝</option>
                                    <option value="天猫" {{ old('platform') == '天猫' ? 'selected' : '' }}>天猫</option>
                                    <option value="京东" {{ old('platform') == '京东' ? 'selected' : '' }}>京东</option>
                                    <option value="拼多多" {{ old('platform') == '拼多多' ? 'selected' : '' }}>拼多多</option>
                                    <option value="其他" {{ old('platform') == '其他' ? 'selected' : '' }}>其他</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="mb-3">
                                <label for="url" class="form-label">商品URL <span class="text-danger">*</span></label>
                                <input type="url" class="form-control" id="url" name="url" value="{{ old('url') }}" required>
                                <div class="form-text">请输入商品的完整链接地址</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="price" class="form-label">当前价格</label>
                                <input type="number" class="form-control" id="price" name="price" value="{{ old('price') }}" step="0.01" min="0">
                                <div class="form-text">可选，系统会自动获取</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="data_source_id" class="form-label">数据源 <span class="text-danger">*</span></label>
                                <select class="form-select" id="data_source_id" name="data_source_id" required>
                                    <option value="">请选择数据源</option>
                                    @foreach($dataSources as $dataSource)
                                        <option value="{{ $dataSource->id }}" {{ old('data_source_id') == $dataSource->id ? 'selected' : '' }}>
                                            {{ $dataSource->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="mb-3">
                                <label for="description" class="form-label">商品描述</label>
                                <textarea class="form-control" id="description" name="description" rows="3" placeholder="请输入商品描述（可选）">{{ old('description') }}</textarea>
                            </div>
                        </div>
                    </div>

                    <!-- 监控设置 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2">监控设置</h6>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="auto_monitor" name="auto_monitor" value="1" {{ old('auto_monitor') ? 'checked' : '' }}>
                                <label class="form-check-label" for="auto_monitor">
                                    自动启用价格监控
                                </label>
                                <div class="form-text">勾选后系统将自动监控此商品的价格变化</div>
                            </div>
                        </div>
                    </div>

                    <!-- 提交按钮 -->
                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex justify-content-end gap-2">
                                <a href="{{ route('products.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-times me-1"></i>取消
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>添加商品
                                </button>
                            </div>
                        </div>
                    </div>
                </form>

                <!-- 批量添加表单 -->
                <form id="batchAddForm" method="POST" style="display: none;">
                    @csrf
                    
                    <!-- 基本信息 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2">批量商品信息</h6>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="batch_platform" class="form-label">电商平台 <span class="text-danger">*</span></label>
                                <select class="form-select" id="batch_platform" name="platform" required>
                                    <option value="">请选择平台</option>
                                    <option value="淘宝">淘宝</option>
                                    <option value="天猫">天猫</option>
                                    <option value="京东">京东</option>
                                    <option value="拼多多">拼多多</option>
                                    <option value="其他">其他</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="batch_data_source_id" class="form-label">数据源 <span class="text-danger">*</span></label>
                                <select class="form-select" id="batch_data_source_id" name="data_source_id" required>
                                    <option value="">请选择数据源</option>
                                    @foreach($dataSources as $dataSource)
                                        <option value="{{ $dataSource->id }}">{{ $dataSource->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="mb-3">
                                <label for="products" class="form-label">商品URL列表 <span class="text-danger">*</span></label>
                                <textarea class="form-control" id="products" name="products" rows="10" 
                                    placeholder="请输入商品URL，一行一个：&#10;https://item.taobao.com/item.htm?id=123456&#10;https://item.jd.com/123456.html&#10;https://mobile.yangkeduo.com/goods.html?goods_id=123456" required></textarea>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    支持淘宝、天猫、京东、拼多多等平台的商品URL，每行一个URL
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 监控设置 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2">监控设置</h6>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="batch_auto_monitor" name="auto_monitor" value="1">
                                <label class="form-check-label" for="batch_auto_monitor">
                                    自动启用价格监控
                                </label>
                                <div class="form-text">勾选后系统将自动监控这些商品的价格变化</div>
                            </div>
                        </div>
                    </div>

                    <!-- 提交按钮 -->
                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex justify-content-end gap-2">
                                <a href="{{ route('products.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-times me-1"></i>取消
                                </a>
                                <button type="button" class="btn btn-info" onclick="validateUrls()">
                                    <i class="fas fa-check me-1"></i>验证URL
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>批量添加
                                </button>
                            </div>
                        </div>
                    </div>
                </form>

            </div>
        </div>
    </div>
</div>

<!-- 结果显示模态框 -->
<div class="modal fade" id="resultModal" tabindex="-1" aria-labelledby="resultModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="resultModalLabel">批量添加结果</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="resultModalBody">
                <!-- 结果内容将通过JavaScript填充 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <a href="{{ route('products.index') }}" class="btn btn-primary">查看商品列表</a>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 添加方式切换
    const addTypeSingle = document.getElementById('addTypeSingle');
    const addTypeBatch = document.getElementById('addTypeBatch');
    const singleAddForm = document.getElementById('singleAddForm');
    const batchAddForm = document.getElementById('batchAddForm');
    
    addTypeSingle.addEventListener('change', function() {
        if (this.checked) {
            singleAddForm.style.display = 'block';
            batchAddForm.style.display = 'none';
        }
    });
    
    addTypeBatch.addEventListener('change', function() {
        if (this.checked) {
            singleAddForm.style.display = 'none';
            batchAddForm.style.display = 'block';
        }
    });
    
    // 批量添加表单提交
    batchAddForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>处理中...';
        
        fetch('{{ route("products.bulk-create") }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showResultModal(data.message, data.errors || []);
                batchAddForm.reset();
            } else {
                alert('添加失败：' + (data.message || '未知错误'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('网络错误，请重试');
        })
        .finally(() => {
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        });
    });
});

// URL验证函数
function validateUrls() {
    const urlsText = document.getElementById('products').value.trim();
    if (!urlsText) {
        alert('请先输入商品URL');
        return;
    }
    
    const urls = urlsText.split('\n').filter(url => url.trim());
    const validUrls = [];
    const invalidUrls = [];
    
    urls.forEach((url, index) => {
        const trimmedUrl = url.trim();
        if (isValidProductUrl(trimmedUrl)) {
            validUrls.push(trimmedUrl);
        } else {
            invalidUrls.push(`第 ${index + 1} 行：${trimmedUrl}`);
        }
    });
    
    let message = `验证完成！\n有效URL：${validUrls.length} 个\n无效URL：${invalidUrls.length} 个`;
    
    if (invalidUrls.length > 0) {
        message += '\n\n无效URL列表：\n' + invalidUrls.join('\n');
    }
    
    alert(message);
}

// 检查是否是有效的商品URL
function isValidProductUrl(url) {
    if (!url || !url.startsWith('http')) {
        return false;
    }
    
    const supportedDomains = [
        'item.taobao.com',
        'detail.tmall.com',
        'item.jd.com',
        'mobile.yangkeduo.com',
        'yangkeduo.com'
    ];
    
    return supportedDomains.some(domain => url.includes(domain));
}

// 显示结果模态框
function showResultModal(message, errors) {
    const modalBody = document.getElementById('resultModalBody');
    let content = `<div class="alert alert-success">${message}</div>`;
    
    if (errors && errors.length > 0) {
        content += '<div class="alert alert-warning"><h6>以下URL处理失败：</h6><ul>';
        errors.forEach(error => {
            content += `<li>${error}</li>`;
        });
        content += '</ul></div>';
    }
    
    modalBody.innerHTML = content;
    
    const modal = new bootstrap.Modal(document.getElementById('resultModal'));
    modal.show();
}
</script>
@endsection 
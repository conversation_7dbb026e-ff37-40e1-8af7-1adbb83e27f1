<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\AlertRule;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class AlertRuleController extends Controller
{
    /**
     * 获取告警规则列表
     */
    public function index(Request $request): JsonResponse
    {
        $query = AlertRule::with(['monitorTask', 'taskGroup']);

        // 搜索功能
        if ($request->filled('search')) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('name', 'like', "%{$searchTerm}%")
                  ->orWhere('description', 'like', "%{$searchTerm}%");
            });
        }

        // 类型筛选
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // 状态筛选
        if ($request->filled('is_enabled')) {
            $query->where('is_enabled', $request->boolean('is_enabled'));
        }

        // 分页
        $perPage = $request->get('per_page', 15);
        $alertRules = $query->orderBy('created_at', 'desc')->paginate($perPage);

        return response()->json([
            'success' => true,
            'message' => '获取告警规则列表成功',
            'data' => $alertRules
        ]);
    }

    /**
     * 获取单个告警规则详情
     */
    public function show($id): JsonResponse
    {
        try {
            $alertRule = AlertRule::with(['monitorTask', 'taskGroup', 'alertLogs'])->findOrFail($id);

            return response()->json([
                'success' => true,
                'message' => '获取告警规则详情成功',
                'data' => $alertRule
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '告警规则不存在或获取失败',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * 创建新告警规则
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'description' => 'nullable|string',
                'type' => 'required|in:price_change,price_threshold,availability,custom',
                'task_id' => 'nullable|exists:monitor_tasks,id',
                'task_group_id' => 'nullable|exists:task_groups,id',
                'condition' => 'required|json',
                'notification_config' => 'nullable|json',
                'is_enabled' => 'boolean'
            ]);

            $validated['is_enabled'] = $validated['is_enabled'] ?? true;
            
            $alertRule = AlertRule::create($validated);

            return response()->json([
                'success' => true,
                'message' => '告警规则创建成功',
                'data' => $alertRule->load(['monitorTask', 'taskGroup'])
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '告警规则创建失败',
                'error' => $e->getMessage()
            ], 422);
        }
    }

    /**
     * 更新告警规则
     */
    public function update(Request $request, $id): JsonResponse
    {
        try {
            $alertRule = AlertRule::findOrFail($id);

            $validated = $request->validate([
                'name' => 'sometimes|required|string|max:255',
                'description' => 'nullable|string',
                'type' => 'sometimes|required|in:price_change,price_threshold,availability,custom',
                'task_id' => 'nullable|exists:monitor_tasks,id',
                'task_group_id' => 'nullable|exists:task_groups,id',
                'condition' => 'sometimes|required|json',
                'notification_config' => 'nullable|json',
                'is_enabled' => 'boolean'
            ]);

            $alertRule->update($validated);

            return response()->json([
                'success' => true,
                'message' => '告警规则更新成功',
                'data' => $alertRule->load(['monitorTask', 'taskGroup'])
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '告警规则更新失败',
                'error' => $e->getMessage()
            ], 422);
        }
    }

    /**
     * 删除告警规则
     */
    public function destroy($id): JsonResponse
    {
        try {
            $alertRule = AlertRule::findOrFail($id);
            $alertRule->delete();

            return response()->json([
                'success' => true,
                'message' => '告警规则删除成功'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '告警规则删除失败',
                'error' => $e->getMessage()
            ], 422);
        }
    }
} 
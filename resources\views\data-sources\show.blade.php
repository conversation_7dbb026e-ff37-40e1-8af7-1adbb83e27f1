@extends('layouts.app')

@section('title', '查看数据源')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- 页面标题 -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">查看数据源</h1>
                    <p class="text-muted mb-0">查看数据源的详细信息</p>
                </div>
                <div>
                    <a href="{{ route('data-sources.index') }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-1"></i>返回列表
                    </a>
                    <a href="{{ route('data-sources.edit', $dataSource->id) }}" class="btn btn-primary">
                        <i class="fas fa-edit me-1"></i>编辑
                    </a>
                </div>
            </div>

            <!-- 数据源信息卡片 -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-database me-2"></i>{{ $dataSource->name }}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- 基本信息 -->
                        <div class="col-md-6">
                            <h6 class="text-muted">基本信息</h6>
                            <table class="table table-sm">
                                <tr>
                                    <td class="fw-bold">数据源名称:</td>
                                    <td>{{ $dataSource->name }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">平台:</td>
                                    <td>
                                        <span class="badge bg-primary">{{ $dataSource->platform }}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">类型:</td>
                                    <td>
                                        <span class="badge bg-info">{{ strtoupper($dataSource->type) }}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">状态:</td>
                                    <td>
                                        <span class="badge bg-{{ $dataSource->status === 'pending' ? 'warning' : ($dataSource->status === 'processing' ? 'info' : 'success') }}">
                                            {{ $dataSource->status }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">描述:</td>
                                    <td>{{ $dataSource->description ?: '无' }}</td>
                                </tr>
                            </table>
                        </div>

                        <!-- 配置信息 -->
                        <div class="col-md-6">
                            <h6 class="text-muted">配置信息</h6>
                            @if($dataSource->config)
                                <table class="table table-sm">
                                    @if($dataSource->type === 'api')
                                        <tr>
                                            <td class="fw-bold">API地址:</td>
                                            <td>{{ $dataSource->config['api_url'] ?? 'N/A' }}</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">请求方法:</td>
                                            <td>{{ $dataSource->config['api_method'] ?? 'GET' }}</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">更新间隔:</td>
                                            <td>{{ $dataSource->config['update_interval'] ?? 60 }} 分钟</td>
                                        </tr>
                                    @else
                                        <tr>
                                            <td class="fw-bold">文件编码:</td>
                                            <td>{{ $dataSource->config['file_encoding'] ?? 'UTF-8' }}</td>
                                        </tr>
                                        @if($dataSource->type === 'csv')
                                            <tr>
                                                <td class="fw-bold">分隔符:</td>
                                                <td>{{ $dataSource->config['delimiter'] ?? ',' }}</td>
                                            </tr>
                                        @endif
                                    @endif
                                </table>
                            @else
                                <p class="text-muted">无配置信息</p>
                            @endif
                        </div>
                    </div>

                    <!-- 选项信息 -->
                    @if($dataSource->options)
                        <div class="row mt-4">
                            <div class="col-12">
                                <h6 class="text-muted">选项设置</h6>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" disabled {{ ($dataSource->options['auto_update'] ?? false) ? 'checked' : '' }}>
                                            <label class="form-check-label">自动更新</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" disabled {{ ($dataSource->options['deduplication'] ?? false) ? 'checked' : '' }}>
                                            <label class="form-check-label">去重处理</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" disabled {{ ($dataSource->options['data_validation'] ?? false) ? 'checked' : '' }}>
                                            <label class="form-check-label">数据验证</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" disabled {{ ($dataSource->options['error_notification'] ?? false) ? 'checked' : '' }}>
                                            <label class="form-check-label">错误通知</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- 时间信息 -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <h6 class="text-muted">时间信息</h6>
                            <table class="table table-sm">
                                <tr>
                                    <td class="fw-bold">创建时间:</td>
                                    <td>{{ $dataSource->created_at ? $dataSource->created_at->format('Y-m-d H:i:s') : 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">更新时间:</td>
                                    <td>{{ $dataSource->updated_at ? $dataSource->updated_at->format('Y-m-d H:i:s') : 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">最后同步:</td>
                                    <td>{{ $dataSource->last_updated_at ? $dataSource->last_updated_at->format('Y-m-d H:i:s') : '从未同步' }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection 
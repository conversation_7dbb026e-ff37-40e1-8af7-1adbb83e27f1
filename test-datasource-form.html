<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据源创建测试</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select, textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background-color: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background-color: #0056b3; }
        .result { margin-top: 20px; padding: 15px; border-radius: 4px; }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
    </style>
</head>
<body>
    <h1>数据源创建测试</h1>
    
    <form id="testForm">
        <div class="form-group">
            <label for="name">数据源名称 *</label>
            <input type="text" id="name" name="name" value="测试数据源" required>
        </div>
        
        <div class="form-group">
            <label for="platform">平台 *</label>
            <select id="platform" name="platform" required>
                <option value="淘宝">淘宝</option>
                <option value="京东">京东</option>
                <option value="天猫">天猫</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="description">描述</label>
            <textarea id="description" name="description">这是一个测试数据源</textarea>
        </div>
        
        <div class="form-group">
            <label for="type">类型 *</label>
            <select id="type" name="type" required>
                <option value="api">API接口</option>
                <option value="excel">Excel文件</option>
                <option value="csv">CSV文件</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="api_url">API地址</label>
            <input type="url" id="api_url" name="api_url" value="https://api.example.com/products">
        </div>
        
        <div class="form-group">
            <label for="api_method">请求方法</label>
            <select id="api_method" name="api_method">
                <option value="GET">GET</option>
                <option value="POST">POST</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="api_key">API密钥</label>
            <input type="password" id="api_key" name="api_key" value="test-key">
        </div>
        
        <div class="form-group">
            <label for="update_interval">更新频率（分钟）</label>
            <input type="number" id="update_interval" name="update_interval" value="60">
        </div>
        
        <div class="form-group">
            <label for="api_headers">请求头（JSON格式）</label>
            <textarea id="api_headers" name="api_headers">{"Content-Type": "application/json"}</textarea>
        </div>
        
        <div class="form-group">
            <label>
                <input type="checkbox" name="auto_update" checked> 自动更新数据
            </label>
        </div>
        
        <div class="form-group">
            <label>
                <input type="checkbox" name="deduplication" checked> 自动去重
            </label>
        </div>
        
        <div class="form-group">
            <label>
                <input type="checkbox" name="data_validation" checked> 数据验证
            </label>
        </div>
        
        <div class="form-group">
            <label>
                <input type="checkbox" name="error_notification" checked> 错误通知
            </label>
        </div>
        
        <button type="submit">创建数据源</button>
    </form>
    
    <div id="result"></div>
    
    <script>
        document.getElementById('testForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<p>正在提交...</p>';
            
            // 获取表单数据
            const formData = new FormData(this);
            
            // 获取CSRF token
            try {
                const tokenResponse = await fetch('/api/csrf-token');
                const tokenData = await tokenResponse.json();
                formData.append('_token', tokenData.token);
            } catch (error) {
                console.error('获取CSRF token失败:', error);
            }
            
            try {
                const response = await fetch('http://127.0.0.1:8000/data-sources', {
                    method: 'POST',
                    body: formData,
                    credentials: 'include',
                    headers: {
                        'Accept': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const result = await response.text();
                    resultDiv.innerHTML = `<div class="success">
                        <h3>提交成功！</h3>
                        <p>响应状态: ${response.status}</p>
                        <p>响应内容: ${result.substring(0, 500)}...</p>
                    </div>`;
                } else {
                    const errorText = await response.text();
                    resultDiv.innerHTML = `<div class="error">
                        <h3>提交失败</h3>
                        <p>状态码: ${response.status}</p>
                        <p>错误信息: ${errorText}</p>
                    </div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">
                    <h3>网络错误</h3>
                    <p>${error.message}</p>
                </div>`;
            }
        });
    </script>
</body>
</html> 
<?php

namespace App\Http\Controllers;

use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class CategoryController extends Controller
{
    /**
     * 获取分类列表
     */
    public function index(Request $request): JsonResponse
    {
        $query = Category::query();

        // 搜索功能
        if ($request->filled('search')) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('name', 'like', "%{$searchTerm}%")
                  ->orWhere('description', 'like', "%{$searchTerm}%");
            });
        }

        // 状态筛选
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // 分页
        $perPage = $request->get('per_page', 15);
        $categories = $query->orderBy('sort_order')->orderBy('created_at', 'desc')->paginate($perPage);

        return response()->json([
            'success' => true,
            'message' => '获取分类列表成功',
            'data' => $categories
        ]);
    }

    /**
     * 获取单个分类详情
     */
    public function show($id): JsonResponse
    {
        try {
            $category = Category::with(['products'])->findOrFail($id);

            return response()->json([
                'success' => true,
                'message' => '获取分类详情成功',
                'data' => $category
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '分类不存在或获取失败',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * 创建新分类
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255|unique:categories,name',
                'description' => 'nullable|string',
                'parent_id' => 'nullable|exists:categories,id',
                'sort_order' => 'nullable|integer|min:0',
                'status' => 'in:active,inactive'
            ]);

            $validated['status'] = $validated['status'] ?? 'active';
            $validated['sort_order'] = $validated['sort_order'] ?? 0;
            
            $category = Category::create($validated);

            return response()->json([
                'success' => true,
                'message' => '分类创建成功',
                'data' => $category
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '分类创建失败',
                'error' => $e->getMessage()
            ], 422);
        }
    }

    /**
     * 更新分类
     */
    public function update(Request $request, $id): JsonResponse
    {
        try {
            $category = Category::findOrFail($id);

            $validated = $request->validate([
                'name' => 'sometimes|required|string|max:255|unique:categories,name,' . $id,
                'description' => 'nullable|string',
                'parent_id' => 'nullable|exists:categories,id',
                'sort_order' => 'nullable|integer|min:0',
                'status' => 'in:active,inactive'
            ]);

            $category->update($validated);

            return response()->json([
                'success' => true,
                'message' => '分类更新成功',
                'data' => $category
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '分类更新失败',
                'error' => $e->getMessage()
            ], 422);
        }
    }

    /**
     * 删除分类
     */
    public function destroy($id): JsonResponse
    {
        try {
            $category = Category::findOrFail($id);
            
            // 检查是否有关联的产品
            if ($category->products()->count() > 0) {
                return response()->json([
                    'success' => false,
                    'message' => '该分类下还有产品，无法删除'
                ], 400);
            }

            $category->delete();

            return response()->json([
                'success' => true,
                'message' => '分类删除成功'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '分类删除失败',
                'error' => $e->getMessage()
            ], 422);
        }
    }
} 
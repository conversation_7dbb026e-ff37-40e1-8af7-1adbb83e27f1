@extends('layouts.app')

@section('title', '商品监控列表')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">首页</a></li>
    <li class="breadcrumb-item active">商品监控</li>
@endsection

@section('page-title', '商品监控列表')

@section('page-actions')
    <div class="btn-group">
        <a href="{{ route('products.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>添加商品
        </a>
        <a href="{{ route('products.import') }}" class="btn btn-success">
            <i class="fas fa-file-excel me-1"></i>批量导入
        </a>
        <button type="button" class="btn btn-info" onclick="refreshData()">
            <i class="fas fa-sync-alt me-1"></i>刷新数据
        </button>
    </div>
@endsection

@section('content')
<div class="card">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-list me-2"></i>商品列表
                </h6>
            </div>
            <div class="col-md-6">
                <div class="input-group">
                    <input type="text" class="form-control" id="searchInput" placeholder="请输入商品名称或ID进行搜索...">
                    <button class="btn btn-outline-secondary" type="button">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="card-body p-0">
        <!-- 筛选条件 -->
        <div class="row p-3 bg-light border-bottom">
            <div class="col-md-2">
                <select class="form-select form-select-sm" id="platformFilter">
                    <option value="">全部平台</option>
                    <option value="淘宝">淘宝</option>
                    <option value="天猫">天猫</option>
                    <option value="京东">京东</option>
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select form-select-sm" id="statusFilter">
                    <option value="">全部状态</option>
                    <option value="active">运行中</option>
                    <option value="paused">已暂停</option>
                </select>
            </div>
            <div class="col-md-2">
                <button class="btn btn-sm btn-primary" onclick="applyFilters()">
                    <i class="fas fa-filter me-1"></i>筛选
                </button>
            </div>
            <div class="col-md-2">
                <button class="btn btn-sm btn-secondary" onclick="clearFilters()">
                    <i class="fas fa-times me-1"></i>清除
                </button>
            </div>
        </div>
        
        <!-- 数据表格 -->
        <div class="table-responsive">
            <table class="table table-hover mb-0" id="productsTable">
                <thead class="table-light">
                    <tr>
                        <th width="50">
                            <input type="checkbox" id="selectAll">
                        </th>
                        <th>商品信息</th>
                        <th width="100">平台</th>
                        <th width="120">价格</th>
                        <th width="120">目标价格</th>
                        <th width="80">状态</th>
                        <th width="140">最后更新</th>
                        <th width="120">操作</th>
                    </tr>
                </thead>
                <tbody id="productsTableBody">
                    <!-- 数据将通过AJAX加载 -->
                    <tr>
                        <td colspan="8" class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <div class="mt-2">正在加载商品数据...</div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- 分页 -->
        <div class="d-flex justify-content-between align-items-center p-3 border-top">
            <div class="text-muted">
                共 <span id="totalCount">0</span> 条，第 <span id="currentPage">1</span> 页，共 <span id="totalPages">0</span> 页
            </div>
            <nav>
                <ul class="pagination pagination-sm mb-0" id="pagination">
                    <!-- 分页按钮将通过JS生成 -->
                </ul>
            </nav>
        </div>
    </div>
</div>

<!-- 编辑商品模态框 -->
<div class="modal fade" id="editProductModal" tabindex="-1" aria-labelledby="editProductModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editProductModalLabel">编辑商品监控</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editProductForm">
                    <input type="hidden" id="editProductId" name="id">
                    
                    <div class="mb-3">
                        <label for="editProductName" class="form-label">商品名称</label>
                        <input type="text" class="form-control" id="editProductName" name="name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="editProductUrl" class="form-label">商品URL</label>
                        <input type="url" class="form-control" id="editProductUrl" name="url" required>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="editPlatform" class="form-label">电商平台</label>
                            <select class="form-select" id="editPlatform" name="platform" required>
                                <option value="淘宝">淘宝</option>
                                <option value="天猫">天猫</option>
                                <option value="京东">京东</option>
                                <option value="拼多多">拼多多</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="editTargetPrice" class="form-label">目标价格</label>
                            <input type="number" class="form-control" id="editTargetPrice" name="target_price" step="0.01" required>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="editMonitorFrequency" class="form-label">监控频率 (分钟)</label>
                        <input type="number" class="form-control" id="editMonitorFrequency" name="monitor_frequency" min="1" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="submit" class="btn btn-primary" form="editProductForm">保存更改</button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<!-- 先加载API客户端 -->
<script src="{{ asset('js/api-client.js') }}"></script>
<!-- 再加载页面特定JS -->
<script src="{{ asset('js/products.js') }}"></script>
<script>
// 页面特定功能
function refreshData() {
    if (typeof window.loadProducts === 'function') {
        window.loadProducts();
    } else {
        location.reload();
    }
}

function applyFilters() {
    if (typeof window.loadProducts === 'function') {
        window.loadProducts();
    }
}

function clearFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('platformFilter').value = '';
    document.getElementById('statusFilter').value = '';
    if (typeof window.loadProducts === 'function') {
        window.loadProducts();
    }
}
</script>
@endsection

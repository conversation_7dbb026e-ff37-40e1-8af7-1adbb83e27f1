@extends('layouts.app')

@section('title', '批量导入商品')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">首页</a></li>
    <li class="breadcrumb-item"><a href="{{ route('products.index') }}">商品监控</a></li>
    <li class="breadcrumb-item active">批量导入</li>
@endsection

@section('page-title', '批量导入商品')

@section('content')
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-file-excel me-2"></i>Excel文件导入
                </h6>
            </div>
            <div class="card-body">

                {{-- Session Messages & Validation Errors --}}
                @if (session('success'))
                    <div class="alert alert-success">
                        {{ session('success') }}
                    </div>
                @endif
                @if (session('error'))
                    <div class="alert alert-danger">
                        {{ session('error') }}
                    </div>
                @endif
                @if ($errors->any())
                    <div class="alert alert-danger pb-0">
                        <ul class="mb-3">
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif

                <!-- 导入说明 -->
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-2"></i>导入说明</h6>
                    <ul class="mb-0">
                        <li>支持 Excel (.xlsx, .xls) 和 CSV 文件格式</li>
                        <li>文件大小不超过 10MB</li>
                        <li>请使用提供的模板格式，确保列名正确</li>
                        <li>商品名称和URL为必填字段</li>
                        <li>导入前请先下载模板文件参考格式</li>
                    </ul>
                </div>

                <!-- 下载模板 -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h6 class="text-primary border-bottom pb-2">第一步：下载导入模板</h6>
                    </div>
                    <div class="col-12">
                        <div class="d-flex align-items-center p-3 bg-light rounded">
                            <div class="flex-grow-1">
                                <h6 class="mb-1">商品导入模板</h6>
                                <small class="text-muted">包含商品名称、平台、URL等必要字段的Excel模板</small>
                            </div>
                            <a href="{{ route('products.download-template') }}" class="btn btn-outline-primary">
                                <i class="fas fa-download me-1"></i>下载模板
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 导入表单 -->
                <form id="importForm" method="POST" action="{{ route('products.import.store') }}" enctype="multipart/form-data">
                    @csrf
                    
                    <!-- 文件选择 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2">第二步：选择导入文件</h6>
                        </div>
                        <div class="col-12">
                            <div class="mb-3">
                                <label for="excel_file" class="form-label">选择Excel文件 <span class="text-danger">*</span></label>
                                <input type="file" class="form-control" id="excel_file" name="excel_file" 
                                       accept=".xlsx,.xls,.csv" required>
                                <div class="form-text">支持 .xlsx, .xls, .csv 格式，最大 10MB</div>
                            </div>
                        </div>
                    </div>

                    <!-- 导入配置 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2">第三步：配置导入选项</h6>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="data_source_id" class="form-label">关联数据源 <span class="text-danger">*</span></label>
                                <select class="form-select" id="data_source_id" name="data_source_id" required>
                                    <option value="">请选择数据源</option>
                                    @if(isset($dataSources))
                                        @foreach($dataSources as $dataSource)
                                            <option value="{{ $dataSource->id }}" {{ old('data_source_id') == $dataSource->id ? 'selected' : '' }}>
                                                {{ $dataSource->name }}
                                            </option>
                                        @endforeach
                                    @endif
                                </select>
                                <div class="form-text">选择此批次商品归属的数据源</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="default_platform" class="form-label">默认平台</label>
                                <select class="form-select" id="default_platform" name="default_platform">
                                    <option value="">使用文件中的平台信息</option>
                                    <option value="淘宝">淘宝</option>
                                    <option value="天猫">天猫</option>
                                    <option value="京东">京东</option>
                                    <option value="拼多多">拼多多</option>
                                    <option value="其他">其他</option>
                                </select>
                                <div class="form-text">当Excel中平台字段为空时使用此默认值</div>
                            </div>
                        </div>
                    </div>

                    <!-- 处理选项 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2">处理选项</h6>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="skip_duplicates" name="skip_duplicates" value="1" checked>
                                <label class="form-check-label" for="skip_duplicates">
                                    跳过重复商品
                                </label>
                                <div class="form-text">基于商品URL判断是否重复</div>
                            </div>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="auto_monitor" name="auto_monitor" value="1">
                                <label class="form-check-label" for="auto_monitor">
                                    自动启用监控
                                </label>
                                <div class="form-text">导入后自动启用价格监控</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="validate_urls" name="validate_urls" value="1" checked>
                                <label class="form-check-label" for="validate_urls">
                                    验证URL格式
                                </label>
                                <div class="form-text">检查商品URL是否有效</div>
                            </div>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="send_notification" name="send_notification" value="1">
                                <label class="form-check-label" for="send_notification">
                                    完成后发送通知
                                </label>
                                <div class="form-text">导入完成后发送邮件通知</div>
                            </div>
                        </div>
                    </div>

                    <!-- 提交按钮 -->
                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex justify-content-end gap-2">
                                <a href="{{ route('products.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-times me-1"></i>取消
                                </a>
                                <button type="button" class="btn btn-info" onclick="previewFile()">
                                    <i class="fas fa-eye me-1"></i>预览文件
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-upload me-1"></i>开始导入
                                </button>
                            </div>
                        </div>
                    </div>
                </form>

            </div>
        </div>
    </div>
</div>

<!-- 文件预览模态框 -->
<div class="modal fade" id="previewModal" tabindex="-1" aria-labelledby="previewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="previewModalLabel">文件预览</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="previewModalBody">
                <!-- 预览内容将通过JavaScript填充 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="submitImport()">确认导入</button>
            </div>
        </div>
    </div>
</div>

<!-- 导入进度模态框 -->
<div class="modal fade" id="progressModal" tabindex="-1" aria-labelledby="progressModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="progressModalLabel">正在导入</h5>
            </div>
            <div class="modal-body text-center">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">导入中...</span>
                </div>
                <div id="progressText">正在处理文件，请稍候...</div>
                <div class="progress mt-3">
                    <div id="progressBar" class="progress-bar" role="progressbar" style="width: 0%"></div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 文件选择事件
    const fileInput = document.getElementById('excel_file');
    fileInput.addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            validateFile(file);
        }
    });
    
    // 表单提交事件
    const importForm = document.getElementById('importForm');
    importForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const file = fileInput.files[0];
        if (!file) {
            alert('请先选择要导入的文件');
            return;
        }
        
        if (!document.getElementById('data_source_id').value) {
            alert('请选择关联的数据源');
            return;
        }
        
        showProgressModal();
        submitForm();
    });
});

// 验证文件
function validateFile(file) {
    const allowedTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
        'application/vnd.ms-excel', // .xls
        'text/csv' // .csv
    ];
    
    const maxSize = 10 * 1024 * 1024; // 10MB
    
    if (!allowedTypes.includes(file.type)) {
        alert('文件格式不支持，请选择 .xlsx, .xls 或 .csv 文件');
        document.getElementById('excel_file').value = '';
        return false;
    }
    
    if (file.size > maxSize) {
        alert('文件大小超过 10MB 限制');
        document.getElementById('excel_file').value = '';
        return false;
    }
    
    return true;
}

// 预览文件
function previewFile() {
    const file = document.getElementById('excel_file').files[0];
    if (!file) {
        alert('请先选择要导入的文件');
        return;
    }
    
    // 这里可以添加文件预览逻辑
    // 由于浏览器限制，实际的Excel解析需要在服务器端完成
    const modalBody = document.getElementById('previewModalBody');
    modalBody.innerHTML = `
        <div class="alert alert-info">
            <h6>文件信息</h6>
            <ul class="mb-0">
                <li>文件名：${file.name}</li>
                <li>文件大小：${(file.size / 1024 / 1024).toFixed(2)} MB</li>
                <li>文件类型：${file.type}</li>
            </ul>
        </div>
        <div class="alert alert-warning">
            <strong>注意：</strong>文件内容预览需要上传到服务器进行解析。请确认文件格式正确后直接导入。
        </div>
    `;
    
    const modal = new bootstrap.Modal(document.getElementById('previewModal'));
    modal.show();
}

// 提交导入
function submitImport() {
    const previewModal = bootstrap.Modal.getInstance(document.getElementById('previewModal'));
    previewModal.hide();
    
    showProgressModal();
    submitForm();
}

// 显示进度模态框
function showProgressModal() {
    const modal = new bootstrap.Modal(document.getElementById('progressModal'));
    modal.show();
    
    // 模拟进度更新
    let progress = 0;
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');
    
    const interval = setInterval(() => {
        progress += Math.random() * 10;
        if (progress > 90) progress = 90;
        
        progressBar.style.width = progress + '%';
        
        if (progress < 30) {
            progressText.textContent = '正在读取文件...';
        } else if (progress < 60) {
            progressText.textContent = '正在验证数据...';
        } else if (progress < 90) {
            progressText.textContent = '正在保存商品...';
        }
    }, 500);
    
    // 保存interval引用以便后续清除
    window.importProgressInterval = interval;
}

// 提交表单
function submitForm() {
    const formData = new FormData(document.getElementById('importForm'));
    
    fetch('{{ route("products.import.store") }}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => {
        if (response.redirected) {
            // 如果服务器返回重定向，直接跳转
            window.location.href = response.url;
            return;
        }
        return response.json();
    })
    .then(data => {
        if (data && data.success !== undefined) {
            // 清除进度更新
            if (window.importProgressInterval) {
                clearInterval(window.importProgressInterval);
            }
            
            // 更新进度到100%
            document.getElementById('progressBar').style.width = '100%';
            document.getElementById('progressText').textContent = '导入完成！';
            
            setTimeout(() => {
                if (data.success) {
                    alert(data.message || '导入成功！');
                    window.location.href = '{{ route("products.index") }}';
                } else {
                    alert('导入失败：' + (data.message || '未知错误'));
                    location.reload();
                }
            }, 1000);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        
        // 清除进度更新
        if (window.importProgressInterval) {
            clearInterval(window.importProgressInterval);
        }
        
        alert('网络错误，请重试');
        location.reload();
    });
}
</script>
@endsection 
/**
 * 商品监控管理JavaScript功能
 */

// 全局变量
let currentPage = 1;
let itemsPerPage = 10;
let currentSearch = '';
let currentPlatform = '';
let currentStatus = '';

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeProducts();
    bindEvents();
});

// 初始化商品监控功能
function initializeProducts() {
    loadProducts();
    loadPlatforms();
}

// 绑定事件
function bindEvents() {
    // 搜索功能
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(handleSearch, 300));
    }

    // 筛选功能
    const platformFilter = document.getElementById('platformFilter');
    if (platformFilter) {
        platformFilter.addEventListener('change', handlePlatformFilter);
    }

    const statusFilter = document.getElementById('statusFilter');
    if (statusFilter) {
        statusFilter.addEventListener('change', handleStatusFilter);
    }

    // 添加商品表单
    const addProductForm = document.getElementById('addProductForm');
    if (addProductForm) {
        addProductForm.addEventListener('submit', handleAddProduct);
    }

    // 编辑商品表单
    const editProductForm = document.getElementById('editProductForm');
    if (editProductForm) {
        editProductForm.addEventListener('submit', handleEditProduct);
    }
}

// 加载商品列表
async function loadProducts() {
    try {
        showLoading(true);
        
        const params = {
            page: currentPage,
            per_page: itemsPerPage,
            search: currentSearch,
            platform: currentPlatform,
            status: currentStatus
        };
        
        const response = await apiService.getProducts(params);
        
        if (response.success) {
            renderProducts(response.data.data);
            updatePagination(response.data.current_page, response.data.per_page, response.data.total);
        } else {
            throw new Error(response.message || '获取商品列表失败');
        }
        
    } catch (error) {
        console.error('加载商品列表失败:', error);
        showAlert('获取商品列表失败，显示模拟数据', 'warning');
        
        // 使用模拟数据作为后备
        const mockProducts = [
            {
                id: 1,
                name: 'iPhone 15 Pro Max',
                platform: '淘宝',
                current_price: 9999.00,
                target_price: 9500.00,
                status: 'active',
                last_updated: '2024-01-15 10:30:00',
                price_change: -2.5
            },
            {
                id: 2,
                name: '华为Mate60 Pro',
                platform: '京东',
                current_price: 6999.00,
                target_price: 6500.00,
                status: 'active',
                last_updated: '2024-01-15 09:45:00',
                price_change: 1.2
            },
            {
                id: 3,
                name: '小米14 Ultra',
                platform: '天猫',
                current_price: 5999.00,
                target_price: 5800.00,
                status: 'paused',
                last_updated: '2024-01-15 08:20:00',
                price_change: 0
            }
        ];
        
        renderProducts(mockProducts);
        updatePagination(1, 10, 100);
        
    } finally {
        showLoading(false);
    }
}

// 渲染商品列表
function renderProducts(products) {
    const tbody = document.getElementById('productsTableBody');
    if (!tbody) return;

    if (!products || products.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center text-muted py-4">
                    <i class="fas fa-inbox fa-2x mb-2"></i>
                    <div>暂无商品数据</div>
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = products.map(product => `
        <tr>
            <td>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" value="${product.id}">
                </div>
            </td>
            <td>
                <div class="d-flex align-items-center">
                    <img src="${product.image_url || '/images/default-product.png'}" 
                         alt="${product.title}" 
                         class="product-image me-2"
                         style="width: 40px; height: 40px; object-fit: cover; border-radius: 4px;">
                    <div>
                        <div class="fw-bold">${product.title || 'N/A'}</div>
                        <small class="text-muted">ID: ${product.source_id || 'N/A'}</small>
                    </div>
                </div>
            </td>
            <td>
                <span class="badge bg-${getPlatformColor(product.source_platform)}">${product.source_platform || 'N/A'}</span>
            </td>
            <td>
                <div class="price-info">
                    <div class="fw-bold">¥${parseFloat(product.min_price || 0).toFixed(2)}</div>
                </div>
            </td>
            <td>¥${parseFloat(product.max_price || 0).toFixed(2)}</td>
            <td>
                <span class="badge bg-${getStatusColor(product.state)}">${getStatusText(product.state)}</span>
            </td>
            <td>
                <small class="text-muted">${formatDateTime(product.updated_at)}</small>
            </td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button type="button" class="btn btn-outline-primary" onclick="viewProduct(${product.id})" title="查看详情">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="editProduct(${product.id})" title="编辑">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button type="button" class="btn btn-outline-danger" onclick="deleteProduct(${product.id})" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// 加载平台列表
async function loadPlatforms() {
    try {
        const response = await apiService.get('/api/platforms');
        if (response.success) {
            updatePlatformFilter(response.data);
        }
    } catch (error) {
        console.error('加载平台列表失败:', error);
        // 使用默认平台列表
        updatePlatformFilter(['淘宝', '京东', '天猫', '拼多多']);
    }
}

// 更新平台筛选器
function updatePlatformFilter(platforms) {
    const select = document.getElementById('platformFilter');
    if (!select) return;

    const currentValue = select.value;
    select.innerHTML = '<option value="">全部平台</option>';
    
    platforms.forEach(platform => {
        const option = document.createElement('option');
        option.value = platform;
        option.textContent = platform;
        if (platform === currentValue) {
            option.selected = true;
        }
        select.appendChild(option);
    });
}

// 事件处理函数
function handleSearch(event) {
    currentSearch = event.target.value;
    currentPage = 1;
    loadProducts();
}

function handlePlatformFilter(event) {
    currentPlatform = event.target.value;
    currentPage = 1;
    loadProducts();
}

function handleStatusFilter(event) {
    currentStatus = event.target.value;
    currentPage = 1;
    loadProducts();
}

// 添加商品
async function handleAddProduct(event) {
    event.preventDefault();
    
    try {
        const formData = new FormData(event.target);
        const data = Object.fromEntries(formData.entries());
        
        const response = await apiService.createProduct(data);
        
        if (response.success) {
            showAlert('商品添加成功！', 'success');
            
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('addProductModal'));
            modal.hide();
            
            // 重置表单
            event.target.reset();
            
            // 重新加载列表
            loadProducts();
        } else {
            throw new Error(response.message || '添加商品失败');
        }
        
    } catch (error) {
        console.error('添加商品失败:', error);
        showAlert('添加商品失败：' + error.message, 'danger');
    }
}

// 编辑商品
async function editProduct(id) {
    try {
        const response = await apiService.getProduct(id);
        
        if (response.success) {
            const product = response.data;
            
            // 填充编辑表单
            document.getElementById('editProductId').value = product.id;
            document.getElementById('editProductName').value = product.title;
            document.getElementById('editProductUrl').value = product.source_url;
            document.getElementById('editPlatform').value = product.source_platform;
            document.getElementById('editTargetPrice').value = product.min_price;
            
            // 假设第一个监控任务的频率为主要频率
            const frequency = product.monitor_tasks && product.monitor_tasks.length > 0 
                ? product.monitor_tasks[0].frequency 
                : 60;
            document.getElementById('editMonitorFrequency').value = frequency;
            
            // 显示编辑模态框
            const modal = new bootstrap.Modal(document.getElementById('editProductModal'));
            modal.show();
        } else {
            throw new Error(response.message || '获取商品信息失败');
        }
        
    } catch (error) {
        console.error('获取商品信息失败:', error);
        showAlert('获取商品信息失败：' + error.message, 'danger');
    }
}

// 处理编辑商品提交
async function handleEditProduct(event) {
    event.preventDefault();
    
    try {
        const formData = new FormData(event.target);
        const data = Object.fromEntries(formData.entries());
        const productId = data.id;
        
        delete data.id; // 移除ID字段
        
        const response = await apiService.updateProduct(productId, data);
        
        if (response.success) {
            showAlert('商品更新成功！', 'success');
            
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('editProductModal'));
            modal.hide();
            
            // 重新加载列表
            loadProducts();
        } else {
            throw new Error(response.message || '更新商品失败');
        }
        
    } catch (error) {
        console.error('更新商品失败:', error);
        showAlert('更新商品失败：' + error.message, 'danger');
    }
}

// 删除商品
async function deleteProduct(id) {
    if (!confirm('确定要删除这个商品监控吗？此操作不可恢复。')) {
        return;
    }
    
    try {
        const response = await apiService.deleteProduct(id);
        
        if (response.success) {
            showAlert('商品删除成功！', 'success');
            loadProducts();
        } else {
            throw new Error(response.message || '删除商品失败');
        }
        
    } catch (error) {
        console.error('删除商品失败:', error);
        showAlert('删除商品失败：' + error.message, 'danger');
    }
}

// 查看商品详情
function viewProduct(id) {
    window.location.href = `/products/${id}`;
}

// 更新分页
function updatePagination(currentPageNum, perPage, total) {
    const pagination = document.getElementById('pagination');
    const totalCountEl = document.getElementById('totalCount');
    const currentPageEl = document.getElementById('currentPage');
    const totalPagesEl = document.getElementById('totalPages');
    if (!pagination || !totalCountEl || !currentPageEl || !totalPagesEl) return;

    totalCountEl.textContent = total;
    currentPageEl.textContent = currentPageNum;
    
    const totalPages = Math.ceil(total / perPage);
    totalPagesEl.textContent = totalPages;

    pagination.innerHTML = '';

    // "上一页"按钮
    const prevPageItem = document.createElement('li');
    prevPageItem.className = `page-item ${currentPageNum === 1 ? 'disabled' : ''}`;
    prevPageItem.innerHTML = `<a class="page-link" href="#" onclick="changePage(${currentPageNum - 1})">上一页</a>`;
    pagination.appendChild(prevPageItem);

    // 页码按钮
    for (let i = 1; i <= totalPages; i++) {
        const pageItem = document.createElement('li');
        pageItem.className = `page-item ${i === currentPageNum ? 'active' : ''}`;
        pageItem.innerHTML = `<a class="page-link" href="#" onclick="changePage(${i})">${i}</a>`;
        pagination.appendChild(pageItem);
    }

    // "下一页"按钮
    const nextPageItem = document.createElement('li');
    nextPageItem.className = `page-item ${currentPageNum === totalPages ? 'disabled' : ''}`;
    nextPageItem.innerHTML = `<a class="page-link" href="#" onclick="changePage(${currentPageNum + 1})">下一页</a>`;
    pagination.appendChild(nextPageItem);
}

function changePage(page) {
    if (page < 1) return;
    currentPage = page;
    loadProducts();
}

// 工具函数
function getPlatformColor(platform) {
    switch (platform) {
        case '淘宝':
        case 'tmall':
            return 'danger';
        case '京东':
        case 'jd':
            return 'primary';
        case '天猫':
            return 'info';
        default:
            return 'secondary';
    }
}

function getStatusColor(status) {
    switch (status) {
        case 'active': return 'success';
        case 'paused': return 'warning';
        case 'error': return 'danger';
        default: return 'secondary';
    }
}

function getStatusText(status) {
    switch (status) {
        case 'active': return '运行中';
        case 'paused': return '已暂停';
        case 'error': return '错误';
        default: return '未知';
    }
}

function formatDateTime(dateString) {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    if (isNaN(date)) return '无效日期';
    return date.toLocaleString('zh-CN', { hour12: false });
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function showAlert(message, type = 'info') {
    // 创建或获取alert容器
    let alertContainer = document.getElementById('alertContainer');
    if (!alertContainer) {
        alertContainer = document.createElement('div');
        alertContainer.id = 'alertContainer';
        alertContainer.className = 'position-fixed top-0 end-0 p-3';
        alertContainer.style.zIndex = '9999';
        document.body.appendChild(alertContainer);
    }
    
    // 创建alert元素
    const alert = document.createElement('div');
    alert.className = `alert alert-${type} alert-dismissible fade show`;
    alert.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    alertContainer.appendChild(alert);
    
    // 5秒后自动消失
    setTimeout(() => {
        if (alert.parentNode) {
            alert.remove();
        }
    }, 5000);
}

function showLoading(show) {
    const existingSpinner = document.querySelector('.products-loading');
    
    if (show && !existingSpinner) {
        const spinner = document.createElement('div');
        spinner.className = 'products-loading position-fixed top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center';
        spinner.style.backgroundColor = 'rgba(255,255,255,0.8)';
        spinner.style.zIndex = '9998';
        spinner.innerHTML = `
            <div class="text-center">
                <div class="spinner-border text-primary mb-2" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <div class="text-muted">正在加载商品数据...</div>
            </div>
        `;
        document.body.appendChild(spinner);
    } else if (!show && existingSpinner) {
        existingSpinner.remove();
    }
}

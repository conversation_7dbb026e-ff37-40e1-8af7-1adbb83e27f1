<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\TaskGroup;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class TaskGroupController extends Controller
{
    /**
     * 获取任务组列表
     */
    public function index(Request $request): JsonResponse
    {
        $query = TaskGroup::with(['tasks']);

        // 搜索功能
        if ($request->filled('search')) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('name', 'like', "%{$searchTerm}%")
                  ->orWhere('description', 'like', "%{$searchTerm}%");
            });
        }

        // 状态筛选
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // 分页
        $perPage = $request->get('per_page', 15);
        $taskGroups = $query->orderBy('created_at', 'desc')->paginate($perPage);

        return response()->json([
            'success' => true,
            'message' => '获取任务组列表成功',
            'data' => $taskGroups
        ]);
    }

    /**
     * 获取单个任务组详情
     */
    public function show($id): JsonResponse
    {
        try {
            $taskGroup = TaskGroup::with(['tasks'])->findOrFail($id);

            return response()->json([
                'success' => true,
                'message' => '获取任务组详情成功',
                'data' => $taskGroup
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '任务组不存在或获取失败',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * 创建新任务组
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'description' => 'nullable|string',
                'status' => 'in:active,inactive,completed',
                'priority' => 'in:low,medium,high'
            ]);

            $validated['status'] = $validated['status'] ?? 'active';
            $validated['priority'] = $validated['priority'] ?? 'medium';
            
            $taskGroup = TaskGroup::create($validated);

            return response()->json([
                'success' => true,
                'message' => '任务组创建成功',
                'data' => $taskGroup->load(['tasks'])
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '任务组创建失败',
                'error' => $e->getMessage()
            ], 422);
        }
    }

    /**
     * 更新任务组
     */
    public function update(Request $request, $id): JsonResponse
    {
        try {
            $taskGroup = TaskGroup::findOrFail($id);

            $validated = $request->validate([
                'name' => 'sometimes|required|string|max:255',
                'description' => 'nullable|string',
                'status' => 'in:active,inactive,completed',
                'priority' => 'in:low,medium,high'
            ]);

            $taskGroup->update($validated);

            return response()->json([
                'success' => true,
                'message' => '任务组更新成功',
                'data' => $taskGroup->load(['tasks'])
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '任务组更新失败',
                'error' => $e->getMessage()
            ], 422);
        }
    }

    /**
     * 删除任务组
     */
    public function destroy($id): JsonResponse
    {
        try {
            $taskGroup = TaskGroup::findOrFail($id);
            $taskGroup->delete();

            return response()->json([
                'success' => true,
                'message' => '任务组删除成功'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '任务组删除失败',
                'error' => $e->getMessage()
            ], 422);
        }
    }
} 
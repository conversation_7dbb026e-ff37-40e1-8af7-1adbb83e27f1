<?php

namespace App\Http\View\Composers;

use App\Models\AlertLog;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;

class NotificationComposer
{
    /**
     * 将数据绑定到视图。
     *
     * @param  \Illuminate\View\View  $view
     * @return void
     */
    public function compose(View $view)
    {
        if (Auth::check()) {
            $unreadCount = AlertLog::getUnreadCountForUser(Auth::id());
            $view->with('unreadNotificationsCount', $unreadCount);
        } else {
            $view->with('unreadNotificationsCount', 0);
        }
    }
} 
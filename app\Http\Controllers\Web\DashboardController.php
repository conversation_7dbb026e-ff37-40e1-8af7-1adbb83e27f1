<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\MonitorTask;
use App\Models\Product;
use App\Models\AlertLog;
use App\Models\PriceHistory;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Carbon\Carbon;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class DashboardController extends Controller
{
    public function index()
    {
        // 获取统计数据（带默认值）
        $taskCount = MonitorTask::where('status', 'active')->count();
        $productCount = Product::count();
        $priceAlertCount = 0; // 暂时设为0，因为警报系统较复杂
        $todayUpdateCount = PriceHistory::whereDate('created_at', Carbon::today())->count();

        // 获取最新警报（暂时使用示例数据）
        $recentAlerts = collect([
            [
                'title' => '系统运行正常',
                'description' => '所有监控任务正常运行',
                'type' => '系统状态',
                'created_at' => now(),
            ]
        ]);

        return view('dashboard', compact(
            'taskCount',
            'productCount',
            'priceAlertCount',
            'todayUpdateCount',
            'recentAlerts'
        ));
    }

    /**
     * 导出仪表板报告
     */
    public function exportReport(Request $request)
    {
        try {
            $format = $request->get('format', 'excel'); // excel, pdf, csv

            // 获取报告数据
            $reportData = $this->getReportData();

            switch ($format) {
                case 'excel':
                    return $this->exportExcel($reportData);
                case 'csv':
                    return $this->exportCsv($reportData);
                case 'pdf':
                    return $this->exportPdf($reportData);
                default:
                    return response()->json(['error' => '不支持的导出格式'], 400);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '导出失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取报告数据
     */
    private function getReportData()
    {
        $endDate = Carbon::now();
        $startDate = $endDate->copy()->subDays(30);

        return [
            'summary' => [
                'taskCount' => MonitorTask::where('status', 'active')->count(),
                'productCount' => Product::count(),
                'priceAlertCount' => 0, // 暂时设为0
                'todayUpdateCount' => PriceHistory::whereDate('created_at', Carbon::today())->count(),
                'reportDate' => $endDate->format('Y-m-d H:i:s'),
                'period' => $startDate->format('Y-m-d') . ' 至 ' . $endDate->format('Y-m-d')
            ],
            'tasks' => MonitorTask::with('product')
                ->where('created_at', '>=', $startDate)
                ->orderBy('created_at', 'desc')
                ->limit(100)
                ->get(),
            'products' => Product::withCount('monitorTasks')
                ->orderBy('monitor_tasks_count', 'desc')
                ->limit(50)
                ->get(),
            'priceHistory' => PriceHistory::with(['sku.product'])
                ->whereBetween('created_at', [$startDate, $endDate])
                ->orderBy('created_at', 'desc')
                ->limit(200)
                ->get()
        ];
    }

    /**
     * 导出Excel格式报告
     */
    private function exportExcel($data)
    {
        $spreadsheet = new Spreadsheet();

        // 创建概览工作表
        $this->createSummarySheet($spreadsheet, $data['summary']);

        // 创建监控任务工作表
        $this->createTasksSheet($spreadsheet, $data['tasks']);

        // 创建商品列表工作表
        $this->createProductsSheet($spreadsheet, $data['products']);

        // 创建价格历史工作表
        $this->createPriceHistorySheet($spreadsheet, $data['priceHistory']);

        // 设置活动工作表为概览
        $spreadsheet->setActiveSheetIndex(0);

        $writer = new Xlsx($spreadsheet);

        $filename = '仪表板报告_' . date('Y-m-d_H-i-s') . '.xlsx';
        $tempFile = tempnam(sys_get_temp_dir(), 'dashboard_report');

        $writer->save($tempFile);

        return response()->download($tempFile, $filename)->deleteFileAfterSend(true);
    }

    /**
     * 创建概览工作表
     */
    private function createSummarySheet($spreadsheet, $summary)
    {
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setTitle('概览');

        // 设置标题
        $sheet->setCellValue('A1', '仪表板数据概览');
        $sheet->mergeCells('A1:B1');
        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(16);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        // 设置数据
        $row = 3;
        $sheet->setCellValue('A' . $row, '报告生成时间');
        $sheet->setCellValue('B' . $row, $summary['reportDate']);
        $row++;

        $sheet->setCellValue('A' . $row, '统计周期');
        $sheet->setCellValue('B' . $row, $summary['period']);
        $row++;

        $sheet->setCellValue('A' . $row, '活跃监控任务');
        $sheet->setCellValue('B' . $row, $summary['taskCount']);
        $row++;

        $sheet->setCellValue('A' . $row, '监控商品数量');
        $sheet->setCellValue('B' . $row, $summary['productCount']);
        $row++;

        $sheet->setCellValue('A' . $row, '价格异常警报');
        $sheet->setCellValue('B' . $row, $summary['priceAlertCount']);
        $row++;

        $sheet->setCellValue('A' . $row, '今日数据更新');
        $sheet->setCellValue('B' . $row, $summary['todayUpdateCount']);

        // 设置列宽
        $sheet->getColumnDimension('A')->setWidth(20);
        $sheet->getColumnDimension('B')->setWidth(30);

        // 设置边框
        $sheet->getStyle('A3:B' . $row)->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                ],
            ],
        ]);
    }

    /**
     * 创建监控任务工作表
     */
    private function createTasksSheet($spreadsheet, $tasks)
    {
        $sheet = $spreadsheet->createSheet();
        $sheet->setTitle('监控任务');

        // 设置标题行
        $headers = ['ID', '任务名称', '商品名称', '状态', '创建时间', '最后更新'];
        $col = 'A';
        foreach ($headers as $header) {
            $sheet->setCellValue($col . '1', $header);
            $sheet->getStyle($col . '1')->getFont()->setBold(true);
            $col++;
        }

        // 填充数据
        $row = 2;
        foreach ($tasks as $task) {
            $sheet->setCellValue('A' . $row, $task->id);
            $sheet->setCellValue('B' . $row, $task->name ?? '监控任务');
            $sheet->setCellValue('C' . $row, $task->product?->title ?? '未知商品');
            $sheet->setCellValue('D' . $row, $task->status);
            $sheet->setCellValue('E' . $row, $task->created_at->format('Y-m-d H:i:s'));
            $sheet->setCellValue('F' . $row, $task->updated_at->format('Y-m-d H:i:s'));
            $row++;
        }

        // 自动调整列宽
        foreach (range('A', 'F') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }
    }

    /**
     * 创建商品列表工作表
     */
    private function createProductsSheet($spreadsheet, $products)
    {
        $sheet = $spreadsheet->createSheet();
        $sheet->setTitle('商品列表');

        // 设置标题行
        $headers = ['ID', '商品名称', '品牌', '分类', '监控任务数', '创建时间'];
        $col = 'A';
        foreach ($headers as $header) {
            $sheet->setCellValue($col . '1', $header);
            $sheet->getStyle($col . '1')->getFont()->setBold(true);
            $col++;
        }

        // 填充数据
        $row = 2;
        foreach ($products as $product) {
            $sheet->setCellValue('A' . $row, $product->id);
            $sheet->setCellValue('B' . $row, $product->title);
            $sheet->setCellValue('C' . $row, $product->brand ?? '');
            $sheet->setCellValue('D' . $row, $product->category ?? '');
            $sheet->setCellValue('E' . $row, $product->monitor_tasks_count);
            $sheet->setCellValue('F' . $row, $product->created_at->format('Y-m-d H:i:s'));
            $row++;
        }

        // 自动调整列宽
        foreach (range('A', 'F') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }
    }

    /**
     * 创建价格历史工作表
     */
    private function createPriceHistorySheet($spreadsheet, $priceHistory)
    {
        $sheet = $spreadsheet->createSheet();
        $sheet->setTitle('价格历史');

        // 设置标题行
        $headers = ['ID', '商品名称', '价格', '原价', '平台', '记录时间'];
        $col = 'A';
        foreach ($headers as $header) {
            $sheet->setCellValue($col . '1', $header);
            $sheet->getStyle($col . '1')->getFont()->setBold(true);
            $col++;
        }

        // 填充数据
        $row = 2;
        foreach ($priceHistory as $history) {
            $sheet->setCellValue('A' . $row, $history->id);
            $sheet->setCellValue('B' . $row, $history->sku?->product?->title ?? '未知商品');
            $sheet->setCellValue('C' . $row, $history->price);
            $sheet->setCellValue('D' . $row, $history->sub_price ?? '');
            $sheet->setCellValue('E' . $row, $history->data_source ?? '');
            $sheet->setCellValue('F' . $row, $history->created_at->format('Y-m-d H:i:s'));
            $row++;
        }

        // 自动调整列宽
        foreach (range('A', 'F') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }
    }

    /**
     * 导出CSV格式报告
     */
    private function exportCsv($data)
    {
        $filename = '仪表板报告_' . date('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($data) {
            $file = fopen('php://output', 'w');

            // 添加BOM以支持中文
            fwrite($file, "\xEF\xBB\xBF");

            // 写入概览数据
            fputcsv($file, ['仪表板数据概览']);
            fputcsv($file, ['报告生成时间', $data['summary']['reportDate']]);
            fputcsv($file, ['统计周期', $data['summary']['period']]);
            fputcsv($file, ['活跃监控任务', $data['summary']['taskCount']]);
            fputcsv($file, ['监控商品数量', $data['summary']['productCount']]);
            fputcsv($file, ['价格异常警报', $data['summary']['priceAlertCount']]);
            fputcsv($file, ['今日数据更新', $data['summary']['todayUpdateCount']]);
            fputcsv($file, []);

            // 写入监控任务数据
            fputcsv($file, ['监控任务列表']);
            fputcsv($file, ['ID', '任务名称', '商品名称', '状态', '创建时间']);
            foreach ($data['tasks'] as $task) {
                fputcsv($file, [
                    $task->id,
                    $task->name ?? '监控任务',
                    $task->product?->title ?? '未知商品',
                    $task->status,
                    $task->created_at->format('Y-m-d H:i:s')
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * 导出PDF格式报告（简化版本）
     */
    private function exportPdf($data)
    {
        // 这里可以使用 dompdf 或其他PDF库
        // 暂时返回错误信息
        return response()->json([
            'success' => false,
            'message' => 'PDF导出功能开发中，请使用Excel或CSV格式'
        ], 501);
    }
}

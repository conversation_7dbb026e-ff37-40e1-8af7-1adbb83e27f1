<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Validator;

class SystemSettingsController extends Controller
{
    /**
     * 显示系统设置页面
     */
    public function index()
    {
        $settings = $this->getSystemSettings();
        return view('system-settings.index', compact('settings'));
    }

    /**
     * 更新系统设置
     */
    public function update(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'site_title' => 'required|string|max:255',
            'site_description' => 'nullable|string|max:500',
            'admin_email' => 'required|email',
            'api_rate_limit' => 'required|integer|min:1|max:10000',
            'session_timeout' => 'required|integer|min:5|max:1440',
            'enable_registration' => 'boolean',
            'enable_email_notifications' => 'boolean',
            'enable_sms_notifications' => 'boolean',
            'maintenance_mode' => 'boolean',
            'debug_mode' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $settings = $request->only([
                'site_title',
                'site_description', 
                'admin_email',
                'api_rate_limit',
                'session_timeout',
                'enable_registration',
                'enable_email_notifications',
                'enable_sms_notifications',
                'maintenance_mode',
                'debug_mode'
            ]);

            // 保存设置到缓存
            Cache::put('system_settings', $settings, now()->addDays(30));

            // 如果是AJAX请求，返回JSON
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => '系统设置已更新'
                ]);
            }

            return redirect()->route('system-settings.index')
                           ->with('success', '系统设置已更新');

        } catch (\Exception $e) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => '更新失败: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()
                           ->with('error', '更新失败: ' . $e->getMessage());
        }
    }

    /**
     * 重置系统设置为默认值
     */
    public function reset()
    {
        try {
            Cache::forget('system_settings');
            
            return response()->json([
                'success' => true,
                'message' => '系统设置已重置为默认值'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '重置失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取系统设置
     */
    private function getSystemSettings()
    {
        return Cache::get('system_settings', [
            'site_title' => '电商市场监测系统',
            'site_description' => '专业的电商商品价格监控和市场分析平台',
            'admin_email' => '<EMAIL>',
            'api_rate_limit' => 1000,
            'session_timeout' => 120,
            'enable_registration' => true,
            'enable_email_notifications' => true,
            'enable_sms_notifications' => false,
            'maintenance_mode' => false,
            'debug_mode' => false,
        ]);
    }

    /**
     * 获取公开设置（用于前端）
     */
    public function getPublicSettings()
    {
        $settings = $this->getSystemSettings();
        
        // 只返回公开的设置
        return response()->json([
            'site_title' => $settings['site_title'],
            'site_description' => $settings['site_description'],
            'enable_registration' => $settings['enable_registration'],
            'maintenance_mode' => $settings['maintenance_mode'],
        ]);
    }

    /**
     * 测试邮件配置
     */
    public function testEmail(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'test_email' => 'required|email'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '邮箱地址无效'
            ], 422);
        }

        try {
            // 这里应该发送测试邮件
            // Mail::to($request->test_email)->send(new TestMail());
            
            return response()->json([
                'success' => true,
                'message' => '测试邮件已发送到 ' . $request->test_email
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '邮件发送失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 清除系统缓存
     */
    public function clearCache()
    {
        try {
            Cache::flush();
            
            return response()->json([
                'success' => true,
                'message' => '系统缓存已清除'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '清除缓存失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取系统信息
     */
    public function getSystemInfo()
    {
        return response()->json([
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version(),
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'database_type' => config('database.default'),
            'cache_driver' => config('cache.default'),
            'queue_driver' => config('queue.default'),
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
            'upload_max_filesize' => ini_get('upload_max_filesize'),
            'disk_free_space' => $this->formatBytes(disk_free_space('.')),
            'disk_total_space' => $this->formatBytes(disk_total_space('.')),
        ]);
    }

    /**
     * 格式化字节数
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}

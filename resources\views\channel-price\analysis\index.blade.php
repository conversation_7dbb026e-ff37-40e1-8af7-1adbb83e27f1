@extends('layouts.app')

@section('title', '渠道价格监测 - 数据分析')

@section('content')
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">渠道价格监测 - 数据分析</h1>
            <p class="text-muted">分析渠道价格偏离率、促销策略等关键指标</p>
        </div>
        <div>
            <button type="button" class="btn btn-success" onclick="exportReport('excel')">
                <i class="fas fa-file-excel me-2"></i>导出Excel
            </button>
            <button type="button" class="btn btn-danger" onclick="exportReport('pdf')">
                <i class="fas fa-file-pdf me-2"></i>导出PDF
            </button>
        </div>
    </div>

    <!-- 任务分组选择 -->
    <div class="card mb-4">
        <div class="card-body">
            <form id="analysisForm">
                <div class="row g-3">
                    <div class="col-md-4">
                        <label for="task_group_id" class="form-label">选择任务分组</label>
                        <select class="form-select" id="task_group_id" name="task_group_id" required>
                            <option value="">请选择任务分组</option>
                            @foreach($taskGroups as $group)
                                <option value="{{ $group->id }}" {{ request('task_group_id') == $group->id ? 'selected' : '' }}>
                                    {{ $group->name }} ({{ $group->data_sources_count }} 个数据源)
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="date_range" class="form-label">分析时间范围</label>
                        <select class="form-select" id="date_range" name="date_range">
                            <option value="7">最近7天</option>
                            <option value="30" selected>最近30天</option>
                            <option value="90">最近90天</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="button" class="btn btn-primary" onclick="performAnalysis()">
                                <i class="fas fa-chart-line me-2"></i>开始分析
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 分析结果 -->
    <div id="analysisResults" style="display: none;">
        <!-- 关键指标概览 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">总商品数</h6>
                                <h3 id="totalProducts">0</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-box fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">促销偏离异常</h6>
                                <h3 id="promotionDeviationCount">0</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-percentage fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">渠道偏离异常</h6>
                                <h3 id="channelDeviationCount">0</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-chart-line fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-danger text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">预警项目</h6>
                                <h3 id="alertCount">0</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-exclamation-triangle fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 促销价偏离率分析 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">促销价偏离率分析</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped" id="promotionDeviationTable">
                        <thead>
                            <tr>
                                <th>商品标题</th>
                                <th>SKU ID</th>
                                <th>原价</th>
                                <th>促销价</th>
                                <th>偏离率</th>
                                <th>数据源</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- 动态填充 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 渠道价格偏离率分析 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">渠道价格偏离率分析</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped" id="channelDeviationTable">
                        <thead>
                            <tr>
                                <th>商品标题</th>
                                <th>SKU ID</th>
                                <th>到手价</th>
                                <th>官方指导价</th>
                                <th>偏离率</th>
                                <th>数据源</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- 动态填充 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 预警项目 -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">预警项目</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped" id="alertTable">
                        <thead>
                            <tr>
                                <th>预警类型</th>
                                <th>商品标题</th>
                                <th>SKU ID</th>
                                <th>预警值</th>
                                <th>阈值</th>
                                <th>严重程度</th>
                                <th>数据源</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- 动态填充 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载状态 -->
    <div id="loadingState" style="display: none;">
        <div class="card">
            <div class="card-body text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">分析中...</span>
                </div>
                <h5 class="mt-3">正在分析数据，请稍候...</h5>
            </div>
        </div>
    </div>

    <!-- 空状态 -->
    <div id="emptyState">
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">选择任务分组开始分析</h5>
                <p class="text-muted">请在上方选择一个任务分组，然后点击"开始分析"按钮</p>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
let currentTaskGroupId = null;

function performAnalysis() {
    const taskGroupId = document.getElementById('task_group_id').value;
    const dateRange = document.getElementById('date_range').value;
    
    if (!taskGroupId) {
        alert('请选择任务分组');
        return;
    }
    
    currentTaskGroupId = taskGroupId;
    
    // 显示加载状态
    document.getElementById('emptyState').style.display = 'none';
    document.getElementById('analysisResults').style.display = 'none';
    document.getElementById('loadingState').style.display = 'block';
    
    // 发送分析请求
    fetch('/channel-price/analysis/calculate', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            task_group_id: taskGroupId,
            date_range: dateRange
        })
    })
    .then(response => response.json())
    .then(data => {
        document.getElementById('loadingState').style.display = 'none';
        
        if (data.success) {
            displayAnalysisResults(data.data);
        } else {
            alert('分析失败：' + data.message);
            document.getElementById('emptyState').style.display = 'block';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        document.getElementById('loadingState').style.display = 'none';
        document.getElementById('emptyState').style.display = 'block';
        alert('分析失败，请重试');
    });
}

function displayAnalysisResults(data) {
    // 更新关键指标
    document.getElementById('totalProducts').textContent = data.summary?.total_products || 0;
    document.getElementById('promotionDeviationCount').textContent = data.summary?.promotion_deviation_count || 0;
    document.getElementById('channelDeviationCount').textContent = data.summary?.channel_deviation_count || 0;
    document.getElementById('alertCount').textContent = data.summary?.alert_count || 0;
    
    // 填充促销价偏离率表格
    fillPromotionDeviationTable(data.promotion_deviation_stats || []);
    
    // 填充渠道价格偏离率表格
    fillChannelDeviationTable(data.channel_price_deviation_stats || []);
    
    // 填充预警表格
    fillAlertTable(data.alert_items || []);
    
    // 显示结果
    document.getElementById('analysisResults').style.display = 'block';
}

function fillPromotionDeviationTable(data) {
    const tbody = document.querySelector('#promotionDeviationTable tbody');
    tbody.innerHTML = '';
    
    data.forEach(item => {
        const row = tbody.insertRow();
        row.innerHTML = `
            <td>${item.product_title}</td>
            <td>${item.sku_id}</td>
            <td>¥${parseFloat(item.price).toFixed(2)}</td>
            <td>¥${parseFloat(item.sub_price).toFixed(2)}</td>
            <td>
                <span class="badge ${Math.abs(item.deviation_rate) > 20 ? 'bg-danger' : Math.abs(item.deviation_rate) > 10 ? 'bg-warning' : 'bg-success'}">
                    ${item.deviation_rate}%
                </span>
            </td>
            <td>${item.data_source}</td>
            <td>
                ${Math.abs(item.deviation_rate) > 20 ? '<span class="badge bg-danger">高风险</span>' : 
                  Math.abs(item.deviation_rate) > 10 ? '<span class="badge bg-warning">中风险</span>' : 
                  '<span class="badge bg-success">正常</span>'}
            </td>
        `;
    });
}

function fillChannelDeviationTable(data) {
    const tbody = document.querySelector('#channelDeviationTable tbody');
    tbody.innerHTML = '';
    
    data.forEach(item => {
        const row = tbody.insertRow();
        row.innerHTML = `
            <td>${item.product_title}</td>
            <td>${item.sku_id}</td>
            <td>¥${parseFloat(item.sub_price).toFixed(2)}</td>
            <td>¥${parseFloat(item.official_guide_price).toFixed(2)}</td>
            <td>
                <span class="badge ${Math.abs(item.deviation_rate) > 20 ? 'bg-danger' : Math.abs(item.deviation_rate) > 10 ? 'bg-warning' : 'bg-success'}">
                    ${item.deviation_rate}%
                </span>
            </td>
            <td>${item.data_source}</td>
            <td>
                ${Math.abs(item.deviation_rate) > 20 ? '<span class="badge bg-danger">高风险</span>' : 
                  Math.abs(item.deviation_rate) > 10 ? '<span class="badge bg-warning">中风险</span>' : 
                  '<span class="badge bg-success">正常</span>'}
            </td>
        `;
    });
}

function fillAlertTable(data) {
    const tbody = document.querySelector('#alertTable tbody');
    tbody.innerHTML = '';
    
    const typeMap = {
        'promotion_deviation': '促销价偏离',
        'channel_price_deviation': '渠道价格偏离',
        'data_update_delay': '数据更新延迟'
    };
    
    data.forEach(item => {
        const row = tbody.insertRow();
        row.innerHTML = `
            <td>${typeMap[item.type] || item.type}</td>
            <td>${item.product_title}</td>
            <td>${item.sku_id}</td>
            <td>${item.value}${item.type.includes('deviation') ? '%' : ''}</td>
            <td>${item.threshold}${item.type.includes('deviation') ? '%' : ''}</td>
            <td>
                <span class="badge ${item.severity === 'high' ? 'bg-danger' : item.severity === 'medium' ? 'bg-warning' : 'bg-info'}">
                    ${item.severity === 'high' ? '高' : item.severity === 'medium' ? '中' : '低'}
                </span>
            </td>
            <td>${item.data_source}</td>
        `;
    });
}

function exportReport(format) {
    if (!currentTaskGroupId) {
        alert('请先进行分析');
        return;
    }
    
    const url = `/channel-price/analysis/export?task_group_id=${currentTaskGroupId}&format=${format}`;
    window.open(url, '_blank');
}
</script>
@endpush

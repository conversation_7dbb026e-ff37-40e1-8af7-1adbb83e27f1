@extends('layouts.app')

@section('title', '数据源管理')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">首页</a></li>
    <li class="breadcrumb-item active">数据源管理</li>
@endsection

@section('page-title', '数据源管理')

@section('page-actions')
    <div class="btn-group">
        <a href="{{ route('data-sources.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>添加数据源
        </a>
        <a href="{{ route('data-sources.excel-create') }}" class="btn btn-success">
            <i class="fas fa-file-excel me-1"></i>Excel导入
        </a>
        <a href="{{ route('data-sources.import-history') }}" class="btn btn-info">
            <i class="fas fa-history me-1"></i>导入历史
        </a>
    </div>
@endsection

@section('content')
<div class="row">
    <div class="col-12">
        <!-- 数据源列表 -->
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-database me-2"></i>数据源列表
                </h6>
            </div>
            <div class="card-body">
                <!-- 搜索和筛选 -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="input-group">
                            <input type="text" class="form-control" id="searchInput" placeholder="搜索数据源...">
                            <button class="btn btn-outline-secondary" type="button" onclick="searchDataSources()">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="platformFilter" onchange="filterByPlatform()">
                            <option value="">全部平台</option>
                            <option value="淘宝">淘宝</option>
                            <option value="京东">京东</option>
                            <option value="天猫">天猫</option>
                            <option value="拼多多">拼多多</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="statusFilter" onchange="filterByStatus()">
                            <option value="">全部状态</option>
                            <option value="active">活跃</option>
                            <option value="inactive">非活跃</option>
                            <option value="error">错误</option>
                        </select>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                </th>
                                <th>数据源名称</th>
                                <th>平台</th>
                                <th>类型</th>
                                <th>商品数量</th>
                                <th>状态</th>
                                <th>最后更新</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="dataSourcesTableBody">
                            <!-- 数据将通过JavaScript填充 -->
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div class="text-muted">
                        显示 <span id="showingStart">1</span> 到 <span id="showingEnd">10</span> 条，共 <span id="totalRecords">0</span> 条记录
                    </div>
                    <nav>
                        <ul class="pagination pagination-sm mb-0" id="pagination">
                            <!-- 分页将通过JavaScript生成 -->
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 批量操作模态框 -->
<div class="modal fade" id="batchActionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">批量操作</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>您选择了 <span id="selectedCount">0</span> 个数据源，请选择要执行的操作：</p>
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-success" onclick="batchActivate()">
                        <i class="fas fa-play me-2"></i>批量激活
                    </button>
                    <button type="button" class="btn btn-warning" onclick="batchDeactivate()">
                        <i class="fas fa-pause me-2"></i>批量停用
                    </button>
                    <button type="button" class="btn btn-info" onclick="batchUpdate()">
                        <i class="fas fa-sync me-2"></i>批量更新
                    </button>
                    <button type="button" class="btn btn-danger" onclick="batchDelete()">
                        <i class="fas fa-trash me-2"></i>批量删除
                    </button>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script src="{{ asset('js/api-client.js') }}"></script>
<script>
// 全局变量
const apiService = new ApiService();
let currentPage = 1;
let itemsPerPage = 10;
let selectedItems = [];

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadDataSources();
    bindSearchAndFilterEvents();
});

// 绑定搜索和筛选事件
function bindSearchAndFilterEvents() {
    document.getElementById('searchInput').addEventListener('input', debounce(loadDataSources, 300));
    document.getElementById('platformFilter').addEventListener('change', loadDataSources);
    document.getElementById('statusFilter').addEventListener('change', loadDataSources);
}

// 加载数据源列表
async function loadDataSources() {
    const tableBody = document.getElementById('dataSourcesTableBody');
    tableBody.innerHTML = `<tr><td colspan="8" class="text-center"><div class="spinner-border" role="status"><span class="visually-hidden">加载中...</span></div></td></tr>`;

    try {
        const params = {
            page: currentPage,
            per_page: itemsPerPage,
            search: document.getElementById('searchInput').value,
            platform: document.getElementById('platformFilter').value,
            status: document.getElementById('statusFilter').value
        };
        const response = await apiService.getDataSources(params);
        
        if (response.success && response.data) {
            renderDataSources(response.data.data);
            updatePagination(response.data);
        } else {
            throw new Error(response.message || '获取数据源失败');
        }
    } catch (error) {
        console.error('加载数据源失败:', error);
        tableBody.innerHTML = `<tr><td colspan="8" class="text-center text-danger">加载数据源失败: ${error.message}</td></tr>`;
    }
}

// 渲染数据源列表
function renderDataSources(dataSources) {
    const tbody = document.getElementById('dataSourcesTableBody');
    
    if (!dataSources || dataSources.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center text-muted py-4">
                    <i class="fas fa-database fa-2x mb-2"></i>
                    <div>暂无数据源</div>
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = dataSources.map(source => `
        <tr>
            <td>
                <input type="checkbox" class="item-checkbox" value="${source.id}" onchange="updateSelectedItems()">
            </td>
            <td>
                <div class="fw-bold">${source.name || 'N/A'}</div>
                <small class="text-muted">ID: ${source.id}</small>
            </td>
            <td>
                <span class="badge bg-${getPlatformColor(source.platform)}">${source.platform || 'N/A'}</span>
            </td>
            <td>
                <span class="badge bg-${getTypeColor(source.type)}">${source.type || 'N/A'}</span>
            </td>
            <td>${(source.product_count || 0).toLocaleString()}</td>
            <td>
                <span class="badge bg-${getStatusColor(source.status)}">${getStatusText(source.status)}</span>
            </td>
            <td>
                <small class="text-muted">${formatDateTime(source.last_updated_at)}</small>
            </td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button type="button" class="btn btn-outline-primary" onclick="viewDataSource(${source.id})" title="查看">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="editDataSource(${source.id})" title="编辑">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button type="button" class="btn btn-outline-success" onclick="syncDataSource(${source.id})" title="同步">
                        <i class="fas fa-sync"></i>
                    </button>
                    <button type="button" class="btn btn-outline-danger" onclick="deleteDataSource(${source.id})" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// 更新分页
function updatePagination(paginationData) {
    const { from, to, total, current_page, last_page } = paginationData;
    document.getElementById('showingStart').textContent = from || 0;
    document.getElementById('showingEnd').textContent = to || 0;
    document.getElementById('totalRecords').textContent = total || 0;

    const paginationUl = document.getElementById('pagination');
    paginationUl.innerHTML = '';

    // 上一页
    paginationUl.insertAdjacentHTML('beforeend', `
        <li class="page-item ${current_page === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${current_page - 1})">上一页</a>
        </li>
    `);

    // 页码
    for (let i = 1; i <= last_page; i++) {
        paginationUl.insertAdjacentHTML('beforeend', `
            <li class="page-item ${i === current_page ? 'active' : ''}">
                <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
            </li>
        `);
    }

    // 下一页
    paginationUl.insertAdjacentHTML('beforeend', `
        <li class="page-item ${current_page === last_page ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${current_page + 1})">下一页</a>
        </li>
    `);
}

function changePage(page) {
    currentPage = page;
    loadDataSources();
}

function viewDataSource(id) {
    window.location.href = `/data-sources/${id}`;
}

function editDataSource(id) {
    window.location.href = `/data-sources/${id}/edit`;
}

async function syncDataSource(id) {
    if (!confirm(`确定要立即同步数据源 #${id} 吗？`)) return;
    
    try {
        const response = await fetch(`/data-sources/${id}/sync`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        
        const result = await response.json();
        
        if (result.success) {
            showAlert('数据源同步已启动', 'success');
            loadDataSources(); // 重新加载列表
        } else {
            throw new Error(result.message || '同步失败');
        }
    } catch (error) {
        console.error('同步数据源失败:', error);
        showAlert('同步数据源失败: ' + error.message, 'danger');
    }
}

async function deleteDataSource(id) {
    if (!confirm(`确定要删除数据源 #${id} 吗？此操作不可恢复。`)) return;

    try {
        const response = await apiService.deleteDataSource(id);
        if(response.success) {
            showAlert('数据源删除成功', 'success');
            loadDataSources(); 
        } else {
            throw new Error(response.message || '删除失败');
        }
    } catch(error) {
        console.error('删除数据源失败:', error);
        showAlert('删除数据源失败: ' + (error.data ? error.data.message : error.message), 'danger');
    }
}

// 工具函数
function getPlatformColor(platform) {
    const colors = { '淘宝': 'warning', '京东': 'danger', '天猫': 'primary', '拼多多': 'success' };
    return colors[platform] || 'secondary';
}

function getTypeColor(type) {
    return type === 'API' ? 'info' : 'success';
}

function getStatusColor(status) {
    const colors = { 'active': 'success', 'inactive': 'warning', 'error': 'danger' };
    return colors[status] || 'secondary';
}

function getStatusText(status) {
    const texts = { 'active': '活跃', 'inactive': '非活跃', 'error': '错误' };
    return texts[status] || '未知';
}

function formatDateTime(dateString) {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', { hour12: false });
}

function debounce(func, wait) {
    let timeout;
    return function(...args) {
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(this, args), wait);
    };
}

function showAlert(message, type = 'info') {
    const alertContainer = document.createElement('div');
    alertContainer.className = `alert alert-${type} alert-dismissible fade show position-fixed top-0 end-0 m-3`;
    alertContainer.style.zIndex = 9999;
    alertContainer.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    document.body.appendChild(alertContainer);
    setTimeout(() => alertContainer.remove(), 5000);
}

// 批量操作相关 (待实现)
function toggleSelectAll() {}
function updateSelectedItems() {}
function batchActivate() {}
function batchDeactivate() {}
function batchUpdate() {}
function batchDelete() {}
</script>
@endsection

@extends('layouts.app')

@section('title', '添加渠道价格监测数据源')

@section('content')
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">添加渠道价格监测数据源</h1>
            <p class="text-muted">配置API接口或手动添加数据源进行渠道价格监测</p>
        </div>
        <div>
            <a href="{{ route('channel-price.data-sources.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>返回列表
            </a>
        </div>
    </div>

    <!-- 表单 -->
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">数据源信息</h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('channel-price.data-sources.store') }}" method="POST">
                        @csrf
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="name" class="form-label">数据源名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                       id="name" name="name" value="{{ old('name') }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label for="task_group_id" class="form-label">任务分组 <span class="text-danger">*</span></label>
                                <select class="form-select @error('task_group_id') is-invalid @enderror" 
                                        id="task_group_id" name="task_group_id" required>
                                    <option value="">请选择任务分组</option>
                                    @foreach($taskGroups as $group)
                                        <option value="{{ $group->id }}" {{ old('task_group_id') == $group->id ? 'selected' : '' }}>
                                            {{ $group->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('task_group_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="api_url" class="form-label">API接口地址 <span class="text-danger">*</span></label>
                            <input type="url" class="form-control @error('api_url') is-invalid @enderror" 
                                   id="api_url" name="api_url" value="{{ old('api_url') }}" 
                                   placeholder="https://api.example.com/products" required>
                            @error('api_url')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">请输入完整的API接口地址</div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">描述</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="3" 
                                      placeholder="请输入数据源的详细描述...">{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="sync_frequency" class="form-label">同步频率 <span class="text-danger">*</span></label>
                                <select class="form-select @error('sync_frequency') is-invalid @enderror" 
                                        id="sync_frequency" name="sync_frequency" required>
                                    <option value="manual" {{ old('sync_frequency') === 'manual' ? 'selected' : '' }}>手动同步</option>
                                    <option value="hourly" {{ old('sync_frequency') === 'hourly' ? 'selected' : '' }}>每小时</option>
                                    <option value="daily" {{ old('sync_frequency') === 'daily' ? 'selected' : '' }}>每天</option>
                                    <option value="weekly" {{ old('sync_frequency') === 'weekly' ? 'selected' : '' }}>每周</option>
                                </select>
                                @error('sync_frequency')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label for="official_guide_price" class="form-label">官方指导价</label>
                                <div class="input-group">
                                    <span class="input-group-text">¥</span>
                                    <input type="number" class="form-control @error('official_guide_price') is-invalid @enderror" 
                                           id="official_guide_price" name="official_guide_price" 
                                           value="{{ old('official_guide_price') }}" 
                                           step="0.01" min="0" placeholder="0.00">
                                    @error('official_guide_price')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="form-text">用于计算渠道价格偏离率</div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('channel-price.data-sources.index') }}" class="btn btn-secondary">取消</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>保存数据源
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- 帮助信息 -->
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>填写说明
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6>数据源名称</h6>
                        <p class="text-muted small">为这个数据源起一个便于识别的名称，例如"淘宝官方店铺"。</p>
                    </div>
                    
                    <div class="mb-3">
                        <h6>API接口地址</h6>
                        <p class="text-muted small">输入商品数据的API接口地址，系统将定期从此接口获取价格数据。</p>
                    </div>
                    
                    <div class="mb-3">
                        <h6>同步频率</h6>
                        <p class="text-muted small">
                            <strong>手动同步：</strong>需要手动触发数据同步<br>
                            <strong>每小时：</strong>系统每小时自动同步一次<br>
                            <strong>每天：</strong>系统每天自动同步一次<br>
                            <strong>每周：</strong>系统每周自动同步一次
                        </p>
                    </div>
                    
                    <div class="mb-3">
                        <h6>官方指导价</h6>
                        <p class="text-muted small">设置商品的官方指导价，用于计算渠道价格偏离率。如果不设置，将无法进行偏离率分析。</p>
                    </div>
                </div>
            </div>

            <!-- 快速操作 -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-bolt me-2"></i>快速操作
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('channel-price.data-sources.excel-create') }}" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-file-excel me-2"></i>Excel批量导入
                        </a>
                        <a href="{{ route('channel-price.data-sources.download-template') }}" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-download me-2"></i>下载导入模板
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// 表单验证
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const nameInput = document.getElementById('name');
    const apiUrlInput = document.getElementById('api_url');
    
    // 实时验证API URL格式
    apiUrlInput.addEventListener('blur', function() {
        const url = this.value.trim();
        if (url && !isValidUrl(url)) {
            this.classList.add('is-invalid');
            let feedback = this.parentNode.querySelector('.invalid-feedback');
            if (!feedback) {
                feedback = document.createElement('div');
                feedback.className = 'invalid-feedback';
                this.parentNode.appendChild(feedback);
            }
            feedback.textContent = '请输入有效的URL地址';
        } else {
            this.classList.remove('is-invalid');
        }
    });
    
    function isValidUrl(string) {
        try {
            new URL(string);
            return true;
        } catch (_) {
            return false;
        }
    }
});
</script>
@endpush

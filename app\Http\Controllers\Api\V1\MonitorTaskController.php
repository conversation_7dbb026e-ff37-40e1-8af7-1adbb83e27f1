<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\MonitorTask;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class MonitorTaskController extends Controller
{
    /**
     * 获取监控任务列表
     */
    public function index(Request $request): JsonResponse
    {
        $query = MonitorTask::with(['product', 'taskGroup']);

        // 搜索功能
        if ($request->filled('search')) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('name', 'like', "%{$searchTerm}%")
                  ->orWhere('url', 'like', "%{$searchTerm}%")
                  ->orWhereHas('product', function ($productQuery) use ($searchTerm) {
                      $productQuery->where('name', 'like', "%{$searchTerm}%");
                  });
            });
        }

        // 平台筛选
        if ($request->filled('platform')) {
            $query->where('platform', $request->platform);
        }

        // 状态筛选
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // 是否启用筛选
        if ($request->filled('is_enabled')) {
            $query->where('is_enabled', $request->boolean('is_enabled'));
        }

        // 分页
        $perPage = $request->get('per_page', 15);
        $tasks = $query->orderBy('created_at', 'desc')->paginate($perPage);

        return response()->json([
            'success' => true,
            'message' => '获取监控任务列表成功',
            'data' => $tasks
        ]);
    }

    /**
     * 获取单个监控任务详情
     */
    public function show($id): JsonResponse
    {
        try {
            $task = MonitorTask::with(['product', 'taskGroup', 'priceHistory'])->findOrFail($id);

            return response()->json([
                'success' => true,
                'message' => '获取监控任务详情成功',
                'data' => $task
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '监控任务不存在或获取失败',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * 创建新监控任务
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'url' => 'required|url',
                'platform' => 'required|string|max:50',
                'product_id' => 'required|exists:products,id',
                'task_group_id' => 'nullable|exists:task_groups,id',
                'interval_minutes' => 'required|integer|min:1|max:1440',
                'target_price' => 'nullable|numeric|min:0',
                'is_enabled' => 'boolean',
                'status' => 'in:pending,running,completed,failed,paused'
            ]);

            $validated['is_enabled'] = $validated['is_enabled'] ?? true;
            $validated['status'] = $validated['status'] ?? 'pending';
            
            $task = MonitorTask::create($validated);

            return response()->json([
                'success' => true,
                'message' => '监控任务创建成功',
                'data' => $task->load(['product', 'taskGroup'])
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '监控任务创建失败',
                'error' => $e->getMessage()
            ], 422);
        }
    }

    /**
     * 更新监控任务
     */
    public function update(Request $request, $id): JsonResponse
    {
        try {
            $task = MonitorTask::findOrFail($id);

            $validated = $request->validate([
                'name' => 'sometimes|required|string|max:255',
                'url' => 'sometimes|required|url',
                'platform' => 'sometimes|required|string|max:50',
                'product_id' => 'sometimes|required|exists:products,id',
                'task_group_id' => 'nullable|exists:task_groups,id',
                'interval_minutes' => 'sometimes|required|integer|min:1|max:1440',
                'target_price' => 'nullable|numeric|min:0',
                'is_enabled' => 'boolean',
                'status' => 'in:pending,running,completed,failed,paused'
            ]);

            $task->update($validated);

            return response()->json([
                'success' => true,
                'message' => '监控任务更新成功',
                'data' => $task->load(['product', 'taskGroup'])
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '监控任务更新失败',
                'error' => $e->getMessage()
            ], 422);
        }
    }

    /**
     * 删除监控任务
     */
    public function destroy($id): JsonResponse
    {
        try {
            $task = MonitorTask::findOrFail($id);
            $task->delete();

            return response()->json([
                'success' => true,
                'message' => '监控任务删除成功'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '监控任务删除失败',
                'error' => $e->getMessage()
            ], 422);
        }
    }
} 
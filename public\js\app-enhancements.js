/**
 * 电商监测系统前端增强脚本
 * 解决Bootstrap组件初始化、事件绑定等常见问题
 */

class AppEnhancements {
    constructor() {
        this.initializeComponents();
        this.setupEventListeners();
        this.fixCommonIssues();
    }

    /**
     * 初始化所有Bootstrap组件
     */
    initializeComponents() {
        // 初始化所有下拉菜单
        this.initializeDropdowns();
        
        // 初始化所有折叠组件
        this.initializeCollapses();
        
        // 初始化模态框
        this.initializeModals();
        
        // 初始化工具提示
        this.initializeTooltips();
    }

    /**
     * 初始化下拉菜单
     */
    initializeDropdowns() {
        const dropdowns = document.querySelectorAll('[data-bs-toggle="dropdown"]');
        dropdowns.forEach(element => {
            if (!element.classList.contains('dropdown-initialized')) {
                try {
                    new bootstrap.Dropdown(element);
                    element.classList.add('dropdown-initialized');
                    
                    // 特殊处理通知和用户菜单
                    if (element.closest('.topbar-nav') || element.closest('.user-dropdown')) {
                        this.enhanceSpecialDropdown(element);
                    }
                } catch (error) {
                    console.warn('Failed to initialize dropdown:', error);
                }
            }
        });
    }

    /**
     * 增强特殊下拉菜单（通知、用户菜单）
     */
    enhanceSpecialDropdown(element) {
        element.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            const dropdown = bootstrap.Dropdown.getInstance(element) || new bootstrap.Dropdown(element);
            dropdown.toggle();
        });
    }

    /**
     * 初始化折叠组件
     */
    initializeCollapses() {
        const collapses = document.querySelectorAll('[data-bs-toggle="collapse"]');
        collapses.forEach(element => {
            if (!element.classList.contains('collapse-initialized')) {
                try {
                    const target = element.getAttribute('data-bs-target') || element.getAttribute('href');
                    if (target) {
                        new bootstrap.Collapse(target, { toggle: false });
                        element.classList.add('collapse-initialized');
                    }
                } catch (error) {
                    console.warn('Failed to initialize collapse:', error);
                }
            }
        });
    }

    /**
     * 初始化模态框
     */
    initializeModals() {
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            if (!modal.classList.contains('modal-initialized')) {
                try {
                    new bootstrap.Modal(modal);
                    modal.classList.add('modal-initialized');
                } catch (error) {
                    console.warn('Failed to initialize modal:', error);
                }
            }
        });
    }

    /**
     * 初始化工具提示
     */
    initializeTooltips() {
        const tooltips = document.querySelectorAll('[data-bs-toggle="tooltip"]');
        tooltips.forEach(element => {
            if (!element.classList.contains('tooltip-initialized')) {
                try {
                    new bootstrap.Tooltip(element);
                    element.classList.add('tooltip-initialized');
                } catch (error) {
                    console.warn('Failed to initialize tooltip:', error);
                }
            }
        });
    }

    /**
     * 设置全局事件监听器
     */
    setupEventListeners() {
        // 处理AJAX请求错误
        this.setupAjaxErrorHandling();
        
        // 处理表单提交
        this.setupFormHandling();
        
        // 处理按钮状态
        this.setupButtonStates();
    }

    /**
     * 设置AJAX错误处理
     */
    setupAjaxErrorHandling() {
        // 为所有AJAX请求添加错误处理
        document.addEventListener('ajaxError', function(e) {
            const xhr = e.detail.xhr;
            const status = xhr.status;
            
            if (status === 401) {
                AppEnhancements.showAlert('登录已过期，请重新登录', 'warning');
                setTimeout(() => {
                    window.location.href = '/login';
                }, 2000);
            } else if (status === 403) {
                AppEnhancements.showAlert('您没有权限执行此操作', 'danger');
            } else if (status === 500) {
                AppEnhancements.showAlert('服务器错误，请稍后重试', 'danger');
            }
        });
    }

    /**
     * 设置表单处理
     */
    setupFormHandling() {
        // 为所有表单添加提交状态处理
        document.addEventListener('submit', function(e) {
            const form = e.target;
            const submitBtn = form.querySelector('button[type="submit"]');
            
            if (submitBtn && !submitBtn.classList.contains('btn-loading')) {
                AppEnhancements.setButtonLoading(submitBtn, true);
                
                // 5秒后自动恢复按钮状态（防止卡死）
                setTimeout(() => {
                    AppEnhancements.setButtonLoading(submitBtn, false);
                }, 5000);
            }
        });
    }

    /**
     * 设置按钮状态处理
     */
    setupButtonStates() {
        // 为有data-loading-text属性的按钮添加点击处理
        document.addEventListener('click', function(e) {
            const button = e.target.closest('button[data-loading-text]');
            if (button && !button.disabled) {
                AppEnhancements.setButtonLoading(button, true);
            }
        });
    }

    /**
     * 修复常见问题
     */
    fixCommonIssues() {
        // 修复表格响应式问题
        this.fixTableResponsive();
        
        // 修复导航激活状态
        this.fixNavigationActive();
        
        // 修复搜索功能
        this.fixSearchFunctionality();
    }

    /**
     * 修复表格响应式问题
     */
    fixTableResponsive() {
        const tables = document.querySelectorAll('table:not(.table-responsive table)');
        tables.forEach(table => {
            if (!table.closest('.table-responsive')) {
                const wrapper = document.createElement('div');
                wrapper.className = 'table-responsive';
                table.parentNode.insertBefore(wrapper, table);
                wrapper.appendChild(table);
            }
        });
    }

    /**
     * 修复导航激活状态
     */
    fixNavigationActive() {
        const currentPath = window.location.pathname;
        const navLinks = document.querySelectorAll('#sidebar a');
        
        navLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href && currentPath.startsWith(href) && href !== '/') {
                link.closest('li').classList.add('active');
                
                // 展开父级菜单
                const parentCollapse = link.closest('.collapse');
                if (parentCollapse) {
                    parentCollapse.classList.add('show');
                }
            }
        });
    }

    /**
     * 修复搜索功能
     */
    fixSearchFunctionality() {
        const searchForms = document.querySelectorAll('form[role="search"], .search-form');
        searchForms.forEach(form => {
            const searchInput = form.querySelector('input[type="search"], input[name*="search"]');
            const submitBtn = form.querySelector('button[type="submit"]');
            
            if (searchInput && submitBtn) {
                // 确保搜索表单有正确的事件绑定
                form.addEventListener('submit', function(e) {
                    const query = searchInput.value.trim();
                    if (!query) {
                        e.preventDefault();
                        AppEnhancements.showAlert('请输入搜索关键词', 'warning');
                        return false;
                    }
                    
                    AppEnhancements.setButtonLoading(submitBtn, true, '搜索中...');
                });
            }
        });
    }

    /**
     * 静态方法：显示警告信息
     */
    static showAlert(message, type = 'info', autoHide = true) {
        const alertContainer = AppEnhancements.getAlertContainer();
        const alertId = 'alert-' + Date.now();
        
        const alertHtml = `
            <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
                <i class="fas fa-${AppEnhancements.getAlertIcon(type)} me-2"></i>
                ${AppEnhancements.escapeHtml(message)}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        alertContainer.insertAdjacentHTML('beforeend', alertHtml);
        
        if (autoHide && type !== 'danger') {
            setTimeout(() => {
                const alertElement = document.getElementById(alertId);
                if (alertElement) {
                    const alert = new bootstrap.Alert(alertElement);
                    alert.close();
                }
            }, 5000);
        }
    }

    /**
     * 静态方法：设置按钮加载状态
     */
    static setButtonLoading(button, loading, loadingText = '加载中...') {
        if (loading) {
            button.disabled = true;
            button.classList.add('btn-loading');
            
            if (!button.dataset.originalText) {
                button.dataset.originalText = button.textContent;
            }
            
            button.innerHTML = `<i class="fas fa-spinner fa-spin me-2"></i>${loadingText}`;
        } else {
            button.disabled = false;
            button.classList.remove('btn-loading');
            
            if (button.dataset.originalText) {
                button.textContent = button.dataset.originalText;
            }
        }
    }

    /**
     * 获取或创建警告容器
     */
    static getAlertContainer() {
        let container = document.getElementById('alert-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'alert-container';
            container.className = 'position-fixed top-0 end-0 p-3';
            container.style.zIndex = '9999';
            document.body.appendChild(container);
        }
        return container;
    }

    /**
     * 获取警告图标
     */
    static getAlertIcon(type) {
        const icons = {
            'success': 'check-circle',
            'danger': 'exclamation-triangle',
            'warning': 'exclamation-circle',
            'info': 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    /**
     * HTML转义
     */
    static escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// 在DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 确保Bootstrap已加载
    if (typeof bootstrap !== 'undefined') {
        window.appEnhancements = new AppEnhancements();
    } else {
        console.error('Bootstrap JavaScript未正确加载，无法初始化应用增强功能');
    }
});

// 为动态添加的内容重新初始化组件
window.reinitializeComponents = function() {
    if (window.appEnhancements) {
        window.appEnhancements.initializeComponents();
    }
}; 
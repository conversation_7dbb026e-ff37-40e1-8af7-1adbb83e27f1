@extends('layouts.app')

@section('title', '系统设置')

@section('page-title', '系统设置')

@section('page-actions')
    <div class="btn-group">
        <button type="button" class="btn btn-warning" onclick="resetSettings()">
            <i class="fas fa-undo me-1"></i>重置默认
        </button>
        <button type="button" class="btn btn-info" onclick="clearCache()">
            <i class="fas fa-trash me-1"></i>清除缓存
        </button>
        <button type="button" class="btn btn-secondary" onclick="showSystemInfo()">
            <i class="fas fa-info-circle me-1"></i>系统信息
        </button>
    </div>
@endsection

@section('content')
<div class="container-fluid">
    <div class="row">
        <!-- 设置表单 -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cog me-2"></i>
                        系统配置
                    </h5>
                </div>
                <div class="card-body">
                    <form id="settingsForm">
                        @csrf
                        
                        <!-- 基本设置 -->
                        <div class="mb-4">
                            <h6 class="text-primary border-bottom pb-2">基本设置</h6>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="site_title" class="form-label">网站标题</label>
                                    <input type="text" class="form-control" id="site_title" name="site_title" 
                                           value="{{ $settings['site_title'] }}" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="admin_email" class="form-label">管理员邮箱</label>
                                    <input type="email" class="form-control" id="admin_email" name="admin_email" 
                                           value="{{ $settings['admin_email'] }}" required>
                                </div>
                                <div class="col-12 mb-3">
                                    <label for="site_description" class="form-label">网站描述</label>
                                    <textarea class="form-control" id="site_description" name="site_description" 
                                              rows="3">{{ $settings['site_description'] }}</textarea>
                                </div>
                            </div>
                        </div>

                        <!-- 系统参数 -->
                        <div class="mb-4">
                            <h6 class="text-primary border-bottom pb-2">系统参数</h6>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="api_rate_limit" class="form-label">API速率限制（每小时）</label>
                                    <input type="number" class="form-control" id="api_rate_limit" name="api_rate_limit" 
                                           value="{{ $settings['api_rate_limit'] }}" min="1" max="10000" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="session_timeout" class="form-label">会话超时（分钟）</label>
                                    <input type="number" class="form-control" id="session_timeout" name="session_timeout" 
                                           value="{{ $settings['session_timeout'] }}" min="5" max="1440" required>
                                </div>
                            </div>
                        </div>

                        <!-- 功能开关 -->
                        <div class="mb-4">
                            <h6 class="text-primary border-bottom pb-2">功能开关</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="enable_registration" 
                                               name="enable_registration" {{ $settings['enable_registration'] ? 'checked' : '' }}>
                                        <label class="form-check-label" for="enable_registration">
                                            允许用户注册
                                        </label>
                                    </div>
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="enable_email_notifications" 
                                               name="enable_email_notifications" {{ $settings['enable_email_notifications'] ? 'checked' : '' }}>
                                        <label class="form-check-label" for="enable_email_notifications">
                                            启用邮件通知
                                        </label>
                                    </div>
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="enable_sms_notifications" 
                                               name="enable_sms_notifications" {{ $settings['enable_sms_notifications'] ? 'checked' : '' }}>
                                        <label class="form-check-label" for="enable_sms_notifications">
                                            启用短信通知
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="maintenance_mode" 
                                               name="maintenance_mode" {{ $settings['maintenance_mode'] ? 'checked' : '' }}>
                                        <label class="form-check-label" for="maintenance_mode">
                                            维护模式
                                        </label>
                                    </div>
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="debug_mode" 
                                               name="debug_mode" {{ $settings['debug_mode'] ? 'checked' : '' }}>
                                        <label class="form-check-label" for="debug_mode">
                                            调试模式
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>保存设置
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="col-lg-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-tools me-2"></i>
                        快速操作
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary" onclick="testEmail()">
                            <i class="fas fa-envelope me-2"></i>测试邮件配置
                        </button>
                        <button class="btn btn-outline-success" onclick="backupDatabase()">
                            <i class="fas fa-database me-2"></i>备份数据库
                        </button>
                        <button class="btn btn-outline-warning" onclick="optimizeDatabase()">
                            <i class="fas fa-wrench me-2"></i>优化数据库
                        </button>
                        <button class="btn btn-outline-info" onclick="generateApiKey()">
                            <i class="fas fa-key me-2"></i>生成API密钥
                        </button>
                    </div>
                </div>
            </div>

            <!-- 系统状态 -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-heartbeat me-2"></i>
                        系统状态
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <div class="border-end">
                                <h4 class="text-success">正常</h4>
                                <small class="text-muted">数据库</small>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <h4 class="text-success">在线</h4>
                            <small class="text-muted">缓存</small>
                        </div>
                        <div class="col-6">
                            <div class="border-end">
                                <h4 class="text-warning">队列</h4>
                                <small class="text-muted">3个任务</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-info">存储</h4>
                            <small class="text-muted">85% 可用</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 系统信息模态框 -->
<div class="modal fade" id="systemInfoModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">系统信息</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="systemInfoContent">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    initializeSettings();
});

function initializeSettings() {
    const form = document.getElementById('settingsForm');
    form.addEventListener('submit', handleSettingsSubmit);
}

async function handleSettingsSubmit(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;

    // 处理复选框
    const checkboxes = ['enable_registration', 'enable_email_notifications', 'enable_sms_notifications', 'maintenance_mode', 'debug_mode'];
    checkboxes.forEach(name => {
        if (!formData.has(name)) {
            formData.append(name, '0');
        } else {
            formData.set(name, '1');
        }
    });

    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>保存中...';
    submitBtn.disabled = true;

    try {
        const response = await fetch('{{ route("system-settings.update") }}', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json',
            },
            body: formData
        });

        const data = await response.json();

        if (data.success) {
            showNotification(data.message, 'success');
        } else {
            showNotification(data.message || '保存失败', 'error');
        }
    } catch (error) {
        console.error('保存设置失败:', error);
        showNotification('保存失败，请重试', 'error');
    } finally {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }
}

async function resetSettings() {
    if (!confirm('确定要重置所有设置为默认值吗？此操作不可撤销。')) {
        return;
    }

    try {
        const response = await fetch('{{ route("system-settings.reset") }}', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json',
            }
        });

        const data = await response.json();

        if (data.success) {
            showNotification(data.message, 'success');
            setTimeout(() => location.reload(), 1500);
        } else {
            showNotification(data.message || '重置失败', 'error');
        }
    } catch (error) {
        console.error('重置设置失败:', error);
        showNotification('重置失败，请重试', 'error');
    }
}

async function clearCache() {
    if (!confirm('确定要清除系统缓存吗？')) {
        return;
    }

    try {
        const response = await fetch('{{ route("system-settings.clear-cache") }}', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json',
            }
        });

        const data = await response.json();

        if (data.success) {
            showNotification(data.message, 'success');
        } else {
            showNotification(data.message || '清除缓存失败', 'error');
        }
    } catch (error) {
        console.error('清除缓存失败:', error);
        showNotification('清除缓存失败，请重试', 'error');
    }
}

async function showSystemInfo() {
    const modal = new bootstrap.Modal(document.getElementById('systemInfoModal'));
    modal.show();

    try {
        const response = await fetch('{{ route("system-settings.system-info") }}');
        const data = await response.json();

        const content = document.getElementById('systemInfoContent');
        content.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6>服务器信息</h6>
                    <table class="table table-sm">
                        <tr><td>PHP版本</td><td>${data.php_version}</td></tr>
                        <tr><td>Laravel版本</td><td>${data.laravel_version}</td></tr>
                        <tr><td>服务器软件</td><td>${data.server_software}</td></tr>
                        <tr><td>内存限制</td><td>${data.memory_limit}</td></tr>
                        <tr><td>执行时间限制</td><td>${data.max_execution_time}秒</td></tr>
                        <tr><td>上传文件大小限制</td><td>${data.upload_max_filesize}</td></tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6>系统配置</h6>
                    <table class="table table-sm">
                        <tr><td>数据库类型</td><td>${data.database_type}</td></tr>
                        <tr><td>缓存驱动</td><td>${data.cache_driver}</td></tr>
                        <tr><td>队列驱动</td><td>${data.queue_driver}</td></tr>
                        <tr><td>可用磁盘空间</td><td>${data.disk_free_space}</td></tr>
                        <tr><td>总磁盘空间</td><td>${data.disk_total_space}</td></tr>
                    </table>
                </div>
            </div>
        `;
    } catch (error) {
        console.error('获取系统信息失败:', error);
        document.getElementById('systemInfoContent').innerHTML =
            '<div class="alert alert-danger">获取系统信息失败</div>';
    }
}

async function testEmail() {
    const email = prompt('请输入测试邮箱地址:');
    if (!email) return;

    try {
        const response = await fetch('{{ route("system-settings.test-email") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json',
            },
            body: JSON.stringify({ test_email: email })
        });

        const data = await response.json();

        if (data.success) {
            showNotification(data.message, 'success');
        } else {
            showNotification(data.message || '邮件发送失败', 'error');
        }
    } catch (error) {
        console.error('测试邮件失败:', error);
        showNotification('测试邮件失败，请重试', 'error');
    }
}

function backupDatabase() {
    showNotification('数据库备份功能开发中...', 'info');
}

function optimizeDatabase() {
    showNotification('数据库优化功能开发中...', 'info');
}

function generateApiKey() {
    const apiKey = 'sk-' + Math.random().toString(36).substr(2, 32);
    navigator.clipboard.writeText(apiKey).then(() => {
        showNotification('API密钥已生成并复制到剪贴板: ' + apiKey, 'success');
    }).catch(() => {
        showNotification('API密钥: ' + apiKey, 'info');
    });
}

function showNotification(message, type = 'info') {
    const alertClass = type === 'success' ? 'alert-success' :
                      type === 'error' ? 'alert-danger' :
                      type === 'warning' ? 'alert-warning' : 'alert-info';

    const notification = document.createElement('div');
    notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);
}
</script>
@endsection

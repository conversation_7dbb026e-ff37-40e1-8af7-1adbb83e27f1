@extends('layouts.app')

@section('title', '通知中心')

@section('page-title', '通知中心')

@section('content')
<div class="container-fluid" id="alert-logs-app">
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">我的通知</h6>
        </div>
        <div class="card-body">
            <!-- 筛选表单 -->
            <div class="row mb-3">
                <div class="col-md-3">
                    <input type="text" class="form-control" v-model="filters.search" placeholder="搜索标题或内容...">
                </div>
                <div class="col-md-2">
                    <select class="form-select" v-model="filters.status">
                        <option value="">所有状态</option>
                        <option value="unread">未读</option>
                        <option value="read">已读</option>
                        <option value="resolved">已解决</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" v-model="filters.severity">
                        <option value="">所有级别</option>
                        <option value="low">低</option>
                        <option value="medium">中</option>
                        <option value="high">高</option>
                        <option value="critical">严重</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <input type="date" class="form-control" v-model="filters.date_from">
                </div>
                <div class="col-md-2">
                    <input type="date" class="form-control" v-model="filters.date_to">
                </div>
                <div class="col-md-1">
                    <button class="btn btn-primary" @click="fetchAlerts(1)">筛选</button>
                </div>
            </div>

            <!-- 加载提示 -->
            <div v-if="loading" class="text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
            </div>

            <!-- 列表 -->
            <div v-else-if="alerts.length > 0" class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>标题</th>
                            <th>级别</th>
                            <th>状态</th>
                            <th>时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="alert in alerts" :key="alert.id" :class="{'table-light': alert.status === 'read'}">
                            <td>@{{ alert.alert_title }}</td>
                            <td><span :class="getSeverityClass(alert.severity)">@{{ getSeverityText(alert.severity) }}</span></td>
                            <td>@{{ getStatusText(alert.status) }}</td>
                            <td>@{{ new Date(alert.created_at).toLocaleString() }}</td>
                            <td>
                                <button class="btn btn-sm btn-info" @click="showAlertDetails(alert)">详情</button>
                                <button v-if="alert.status === 'unread'" class="btn btn-sm btn-success" @click="markAsRead(alert.id)">标为已读</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- 空状态 -->
            <div v-else class="text-center py-5">
                <p>没有找到相关通知。</p>
            </div>

            <!-- 分页 -->
            <nav v-if="pagination.last_page > 1">
                <ul class="pagination justify-content-center">
                    <li class="page-item" :class="{ 'disabled': pagination.current_page === 1 }">
                        <a class="page-link" href="#" @click.prevent="fetchAlerts(pagination.current_page - 1)">上一页</a>
                    </li>
                    <li v-for="page in pagination.last_page" :key="page" class="page-item" :class="{ 'active': pagination.current_page === page }">
                        <a class="page-link" href="#" @click.prevent="fetchAlerts(page)">@{{ page }}</a>
                    </li>
                    <li class="page-item" :class="{ 'disabled': pagination.current_page === pagination.last_page }">
                        <a class="page-link" href="#" @click.prevent="fetchAlerts(pagination.current_page + 1)">下一页</a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>

    <!-- 详情模态框 -->
    <div class="modal fade" id="alertDetailsModal" tabindex="-1" aria-labelledby="alertDetailsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="alertDetailsModalLabel">@{{ selectedAlert?.alert_title }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p><strong>消息:</strong> @{{ selectedAlert?.message }}</p>
                    <p><strong>时间:</strong> @{{ selectedAlert ? new Date(selectedAlert.created_at).toLocaleString() : '' }}</p>
                    <p><strong>状态:</strong> @{{ getStatusText(selectedAlert?.status) }}</p>
                    <p><strong>严重程度:</strong> @{{ getSeverityText(selectedAlert?.severity) }}</p>
                    <hr>
                    <h6>关联信息</h6>
                    <ul class="list-group">
                        <li v-if="selectedAlert?.rule" class="list-group-item"><strong>规则:</strong> @{{ selectedAlert.rule.name }}</li>
                        <li v-if="selectedAlert?.task" class="list-group-item"><strong>任务:</strong> @{{ selectedAlert.task.name }}</li>
                        <li v-if="selectedAlert?.sku" class="list-group-item"><strong>SKU:</strong> @{{ selectedAlert.sku.sku_name }} (@{{ selectedAlert.sku.platform }})</li>
                    </ul>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

</div>

@push('scripts')
<!-- Vue and Axios -->
<script src="https://cdn.jsdelivr.net/npm/vue@3.2.37/dist/vue.global.prod.js"></script>
<script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
<script>
    const { createApp } = Vue

    createApp({
        data() {
            // Helper function to format date as YYYY-MM-DD
            const formatDate = (date) => {
                const d = new Date(date);
                const year = d.getFullYear();
                const month = (`0${d.getMonth() + 1}`).slice(-2);
                const day = (`0${d.getDate()}`).slice(-2);
                return `${year}-${month}-${day}`;
            }

            const today = new Date();
            const sevenDaysAgo = new Date();
            sevenDaysAgo.setDate(today.getDate() - 7);

            return {
                loading: true,
                alerts: [],
                pagination: {},
                filters: {
                    search: '',
                    status: '',
                    severity: '',
                    date_from: formatDate(sevenDaysAgo),
                    date_to: formatDate(today)
                },
                selectedAlert: null,
                detailsModal: null
            }
        },
        mounted() {
            this.fetchAlerts();
            this.detailsModal = new bootstrap.Modal(document.getElementById('alertDetailsModal'));
        },
        methods: {
            fetchAlerts(page = 1) {
                this.loading = true;
                let params = { page, ...this.filters };
                axios.get('/api/alert-logs', { params })
                    .then(response => {
                        this.alerts = response.data.data.data;
                        this.pagination = response.data.data;
                    })
                    .catch(error => {
                        console.error("获取通知失败:", error);
                        alert('获取通知失败，请稍后再试。');
                    })
                    .finally(() => {
                        this.loading = false;
                    });
            },
            showAlertDetails(alert) {
                this.selectedAlert = alert;
                this.detailsModal.show();
            },
            markAsRead(id) {
                axios.post(`/api/v1/alert-logs/${id}/mark-as-read`)
                    .then(response => {
                        this.fetchAlerts(this.pagination.current_page);
                    })
                    .catch(error => {
                        console.error("标记已读失败:", error);
                        alert('操作失败，请稍后再试。');
                    });
            },
            getSeverityClass(severity) {
                switch(severity) {
                    case 'critical': return 'badge bg-danger';
                    case 'high': return 'badge bg-warning text-dark';
                    case 'medium': return 'badge bg-info';
                    case 'low': return 'badge bg-secondary';
                    default: return 'badge bg-light text-dark';
                }
            },
            getSeverityText(severity) {
                const map = { critical: '严重', high: '高', medium: '中', low: '低' };
                return map[severity] || '未知';
            },
            getStatusText(status) {
                const map = { unread: '未读', read: '已读', resolved: '已解决', ignored: '已忽略' };
                return map[status] || '未知';
            }
        }
    }).mount('#alert-logs-app')
</script>
@endpush
@endsection 
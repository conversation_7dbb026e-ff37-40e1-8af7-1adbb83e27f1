<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;
use App\Http\Controllers\DataSourceController;
use App\Models\User;

// 初始化Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

// 创建测试请求
$requestData = [
    'name' => '测试数据源',
    'platform' => '淘宝',
    'description' => '这是一个测试数据源',
    'type' => 'api',
    'api_url' => 'https://api.example.com/products',
    'api_method' => 'GET',
    'api_key' => 'test-api-key',
    'update_interval' => 60,
    'api_headers' => '{"Content-Type": "application/json"}',
    'auto_update' => true,
    'deduplication' => true,
    'data_validation' => true,
    'error_notification' => true,
];

$request = Request::create('/data-sources', 'POST', $requestData);

// 模拟用户认证
$user = User::first();
if (!$user) {
    echo "错误：数据库中没有用户，请先创建用户\n";
    exit(1);
}

// 设置认证用户
auth()->login($user);

// 创建控制器实例并调用store方法
$controller = new DataSourceController();

try {
    echo "开始测试数据源创建...\n";
    echo "请求数据：\n";
    print_r($requestData);
    echo "\n";
    
    $response = $controller->store($request);
    
    echo "响应：\n";
    if (method_exists($response, 'getContent')) {
        echo $response->getContent() . "\n";
    } else {
        echo "重定向响应\n";
    }
    
    echo "\n测试完成！\n";
    
} catch (\Exception $e) {
    echo "错误：" . $e->getMessage() . "\n";
    echo "堆栈跟踪：\n" . $e->getTraceAsString() . "\n";
} 
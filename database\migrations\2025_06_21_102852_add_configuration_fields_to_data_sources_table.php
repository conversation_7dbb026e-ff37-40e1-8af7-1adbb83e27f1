<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('data_sources', function (Blueprint $table) {
            // 添加数据源配置字段
            $table->string('name')->nullable()->comment('数据源名称');
            $table->text('description')->nullable()->comment('数据源描述'); 
            $table->enum('type', ['api', 'excel', 'csv', 'url', 'id'])->nullable()->comment('数据源类型');
            $table->json('config')->nullable()->comment('数据源配置（API配置、文件配置等）');
            $table->json('options')->nullable()->comment('数据处理选项');
            
            // 修改现有字段，允许为空（兼容新的配置方式）
            $table->string('source_id')->nullable()->change();
            $table->enum('source_type', ['url', 'id', 'api', 'excel', 'csv'])->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('data_sources', function (Blueprint $table) {
            // 删除添加的字段
            $table->dropColumn(['name', 'description', 'type', 'config', 'options']);
            
            // 恢复原有字段约束
            $table->string('source_id')->nullable(false)->change();
            $table->enum('source_type', ['url', 'id'])->change();
        });
    }
};

<?php
require_once 'vendor/autoload.php';

use App\Http\Controllers\DataSourceController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\User;

// 启动Laravel应用
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "测试数据源表单提交...\n";

try {
    // 获取第一个用户并登录
    $user = User::first();
    if (!$user) {
        echo "错误：没有找到用户，请先创建用户\n";
        exit(1);
    }
    
    Auth::login($user);
    echo "已登录用户: {$user->name} (ID: {$user->id})\n";
    
    // 模拟表单数据
    $formData = [
        'name' => '测试API数据源2',
        'platform' => '淘宝',
        'description' => '这是一个测试用的API数据源',
        'type' => 'api',
        'api_url' => 'https://api.test.com/products',
        'api_method' => 'GET',
        'api_key' => 'test_key_123',
        'update_interval' => '60',
        'api_headers' => '{"Content-Type": "application/json"}',
        'file_encoding' => 'utf-8',
        'delimiter' => ',',
        'column_mapping' => '{}',
        'auto_update' => '1',
        'deduplication' => '1',
        'data_validation' => '1',
        'error_notification' => '1',
    ];
    
    // 创建Request对象
    $request = Request::create('/data-sources', 'POST', $formData);
    $request->headers->set('Content-Type', 'application/x-www-form-urlencoded');
    
    // 创建控制器实例并调用store方法
    $controller = new DataSourceController();
    
    echo "调用控制器store方法...\n";
    $response = $controller->store($request);
    
    echo "响应类型: " . get_class($response) . "\n";
    
    if (method_exists($response, 'getStatusCode')) {
        echo "HTTP状态码: " . $response->getStatusCode() . "\n";
    }
    
    if (method_exists($response, 'getTargetUrl')) {
        echo "重定向URL: " . $response->getTargetUrl() . "\n";
    }
    
    // 检查session中的消息
    $session = app('session');
    if ($session->has('success')) {
        echo "成功消息: " . $session->get('success') . "\n";
    }
    if ($session->has('error')) {
        echo "错误消息: " . $session->get('error') . "\n";
    }
    
    echo "\n测试完成！\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
    exit(1);
} 